package com.wedin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能回复配置
 */
@Configuration
@ConfigurationProperties(prefix = "smart-reply")
@Data
public class SmartReplyConfig {

    /**
     * 是否启用智能回复
     */
    private boolean enabled = true;

    /**
     * 回复延迟配置（毫秒）
     */
    private DelayConfig delay = new DelayConfig();

    /**
     * 回复策略配置
     */
    private StrategyConfig strategy = new StrategyConfig();

    /**
     * 回复模板配置
     */
    private Map<String, String> templates = new HashMap<>();

    @Data
    public static class DelayConfig {
        /**
         * 基础延迟时间（毫秒）
         */
        private long base = 2000;

        /**
         * 重要消息延迟时间（毫秒）
         */
        private long important = 1000;

        /**
         * 普通消息延迟时间（毫秒）
         */
        private long normal = 3000;

        /**
         * 低重要性消息延迟时间（毫秒）
         */
        private long low = 5000;
    }

    @Data
    public static class StrategyConfig {
        /**
         * 最低回复重要性阈值
         */
        private int minImportanceScore = 5;

        /**
         * 是否回复搜索请求
         */
        private boolean replyToSearch = false;

        /**
         * 是否回复统计请求
         */
        private boolean replyToStats = false;

        /**
         * 每日最大回复次数
         */
        private int maxRepliesPerDay = 50;

        /**
         * 回复间隔时间（分钟）
         */
        private int minReplyInterval = 5;
    }

    /**
     * 初始化默认模板
     */
    public SmartReplyConfig() {
        templates.put("TODO", "好的，我已经记录了这个待办事项，请注意及时处理。");
        templates.put("IMPORTANT_INFO", "重要信息已记录，我会特别关注这条信息。");
        templates.put("CONTACT_INFO", "联系信息已保存。");
        templates.put("LEARNING_NOTE", "学习笔记已记录，持续学习很棒！");
        templates.put("PERSONAL_THOUGHT", "您的想法已记录下来。");
        templates.put("FINANCIAL", "财务信息已安全记录。");
        templates.put("HEALTH", "健康相关信息已记录，注意身体健康。");
        templates.put("ENTERTAINMENT", "娱乐信息已记录，劳逸结合很重要。");
        templates.put("OTHER", "消息已收到并记录。");
    }
}
