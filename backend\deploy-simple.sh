#!/bin/bash

# 简化部署脚本 - 避免时区文件挂载问题
echo "开始部署 Wedin AI Assistant 后端服务（简化版）..."

# 停止并删除现有容器
echo "停止现有容器..."
docker-compose down

# 删除旧镜像
echo "删除旧镜像..."
docker rmi backend_wedin-backend 2>/dev/null || true

# 构建新镜像
echo "构建新镜像..."
docker-compose build --no-cache

# 启动服务
echo "启动服务..."
docker-compose up -d

# 等待启动
echo "等待服务启动..."
sleep 10

# 检查状态
echo "检查服务状态..."
docker-compose ps

# 测试时区
echo "测试容器时区..."
docker-compose exec wedin-backend date

echo "部署完成！"
