package com.wedin.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 消息实体类
 * 存储用户发送的消息及AI分析结果
 */
@Entity
@Table(name = "messages")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Message {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户标识（兼容字符串ID）
     */
    @Column(name = "user_identifier", length = 100)
    private String userIdentifier;

    /**
     * 微信消息ID（用于防重复）
     */
    @Column(name = "wechat_msg_id", length = 100)
    private String wechatMsgId;

    /**
     * 消息类型
     */
    @Column(name = "type", length = 20)
    private String type = "TEXT";

    /**
     * 原始消息内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    /**
     * AI理解后的内容摘要
     */
    @Column(name = "ai_summary", columnDefinition = "TEXT")
    private String aiSummary;

    /**
     * AI分类结果
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private MessageCategory category;

    /**
     * AI提取的关键词
     */
    @Column(name = "keywords", length = 500)
    private String keywords;

    /**
     * 重要性评分 (1-10)
     */
    @Column(name = "importance_score")
    private Integer importanceScore;

    /**
     * 是否需要提醒
     */
    @Column(name = "need_reminder")
    private Boolean needReminder;

    /**
     * 提醒时间
     */
    @Column(name = "reminder_time")
    private LocalDateTime reminderTime;

    /**
     * 消息创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 消息更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * AI处理状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "ai_status")
    private AiProcessStatus aiStatus;

    /**
     * AI回复内容（PWA版本）
     */
    @Column(name = "ai_response", columnDefinition = "TEXT")
    private String aiResponse;

    /**
     * 标签（PWA版本）
     */
    @Column(name = "tags", length = 500)
    private String tags;

    /**
     * 重要程度（PWA版本，1-5星）
     */
    @Column(name = "importance")
    private Integer importance;

    /**
     * 对话会话ID（用于关联对话）
     */
    @Column(name = "conversation_id", length = 100)
    private String conversationId;

    /**
     * 父消息ID（用于确认流程）
     */
    @Column(name = "parent_message_id")
    private Long parentMessageId;

    /**
     * 确认状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "confirmation_status")
    private ConfirmationStatus confirmationStatus;

    /**
     * 是否来自用户
     */
    @Column(name = "is_from_user")
    private Boolean isFromUser;

    /**
     * 是否来自AI
     */
    @Column(name = "is_from_ai")
    private Boolean isFromAI;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (aiStatus == null) {
            aiStatus = AiProcessStatus.PENDING;
        }
        if (confirmationStatus == null) {
            confirmationStatus = ConfirmationStatus.PENDING;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * AI处理状态枚举
     */
    public enum AiProcessStatus {
        PENDING,    // 待处理
        PROCESSING, // 处理中
        COMPLETED,  // 已完成
        FAILED      // 处理失败
    }

    /**
     * 确认状态枚举
     */
    public enum ConfirmationStatus {
        PENDING,    // 待确认
        CONFIRMED,  // 已确认
        REJECTED,   // 已拒绝
        MODIFIED    // 已修改
    }
}
