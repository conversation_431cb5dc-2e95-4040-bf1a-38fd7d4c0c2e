package com.wedin.repository;

import com.wedin.entity.DeviceToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 设备token数据访问层
 */
@Repository
public interface DeviceTokenRepository extends JpaRepository<DeviceToken, Long> {

    /**
     * 根据用户ID和token查找设备
     */
    Optional<DeviceToken> findByUserIdAndTokenAndDeviceType(String userId, String token, String deviceType);

    /**
     * 根据用户ID和设备类型查找活跃设备
     */
    List<DeviceToken> findByUserIdAndDeviceTypeAndStatus(String userId, String deviceType, DeviceToken.TokenStatus status);

    /**
     * 根据用户ID查找所有活跃设备
     */
    List<DeviceToken> findByUserIdAndStatus(String userId, DeviceToken.TokenStatus status);

    /**
     * 根据用户ID查找所有设备（按注册时间倒序）
     */
    List<DeviceToken> findByUserIdOrderByRegisteredAtDesc(String userId);

    /**
     * 根据设备类型查找所有活跃设备
     */
    List<DeviceToken> findByDeviceTypeAndStatus(String deviceType, DeviceToken.TokenStatus status);

    /**
     * 根据token查找设备
     */
    Optional<DeviceToken> findByToken(String token);

    /**
     * 统计用户的活跃设备数量
     */
    @Query("SELECT COUNT(dt) FROM DeviceToken dt WHERE dt.userId = :userId AND dt.status = 'ACTIVE'")
    long countActiveTokensByUserId(@Param("userId") String userId);

    /**
     * 统计指定设备类型的活跃设备数量
     */
    @Query("SELECT COUNT(dt) FROM DeviceToken dt WHERE dt.userId = :userId AND dt.deviceType = :deviceType AND dt.status = 'ACTIVE'")
    long countActiveTokensByUserIdAndDeviceType(@Param("userId") String userId, @Param("deviceType") String deviceType);

    /**
     * 查找长时间未活跃的设备token
     */
    @Query("SELECT dt FROM DeviceToken dt WHERE dt.status = 'ACTIVE' AND " +
           "(dt.lastActiveAt IS NULL OR dt.lastActiveAt < :beforeTime)")
    List<DeviceToken> findInactiveTokens(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查找推送失败次数超过指定次数的设备
     */
    @Query("SELECT dt FROM DeviceToken dt WHERE dt.failureCount >= :maxFailures AND dt.status = 'ACTIVE'")
    List<DeviceToken> findTokensWithHighFailureCount(@Param("maxFailures") int maxFailures);

    /**
     * 增加推送失败次数
     */
    @Modifying
    @Transactional
    @Query("UPDATE DeviceToken dt SET dt.failureCount = dt.failureCount + 1, " +
           "dt.updatedAt = :updateTime WHERE dt.id = :id")
    int incrementFailureCount(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 重置失败次数并更新最后推送时间
     */
    @Modifying
    @Transactional
    @Query("UPDATE DeviceToken dt SET dt.failureCount = 0, dt.lastPushAt = :pushTime, " +
           "dt.lastActiveAt = :activeTime, dt.updatedAt = :updateTime WHERE dt.id = :id")
    int resetFailureCountAndUpdatePushTime(@Param("id") Long id,
                                          @Param("pushTime") LocalDateTime pushTime,
                                          @Param("activeTime") LocalDateTime activeTime,
                                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新设备状态
     */
    @Modifying
    @Transactional
    @Query("UPDATE DeviceToken dt SET dt.status = :status, dt.updatedAt = :updateTime " +
           "WHERE dt.id IN :ids")
    int updateStatusByIds(@Param("ids") List<Long> ids,
                         @Param("status") DeviceToken.TokenStatus status,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 删除过期的设备token
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM DeviceToken dt WHERE dt.status = 'EXPIRED' AND dt.updatedAt < :beforeTime")
    int deleteExpiredTokens(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 更新最后活跃时间
     */
    @Modifying
    @Transactional
    @Query("UPDATE DeviceToken dt SET dt.lastActiveAt = :activeTime, dt.updatedAt = :updateTime " +
           "WHERE dt.id = :id")
    int updateLastActiveTime(@Param("id") Long id,
                            @Param("activeTime") LocalDateTime activeTime,
                            @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据用户ID和设备类型删除设备
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM DeviceToken dt WHERE dt.userId = :userId AND dt.deviceType = :deviceType")
    int deleteByUserIdAndDeviceType(@Param("userId") String userId, @Param("deviceType") String deviceType);

    /**
     * 查找指定时间之前注册的设备
     */
    @Query("SELECT dt FROM DeviceToken dt WHERE dt.registeredAt < :beforeTime")
    List<DeviceToken> findTokensRegisteredBefore(@Param("beforeTime") LocalDateTime beforeTime);
}
