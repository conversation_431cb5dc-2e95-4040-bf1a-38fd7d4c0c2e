package com.wedin.service;

import com.wedin.config.SmartReplyConfig;
import com.wedin.entity.Message;
import com.wedin.entity.MessageCategory;
import com.wedin.repository.MessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 智能回复服务
 * 负责生成和发送智能回复消息
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SmartReplyService {

    private final DeepSeekService deepSeekService;
    private final MessageRepository messageRepository;
    private final SmartReplyConfig smartReplyConfig;

    @Qualifier("messageAnalysisExecutor")
    private final Executor messageAnalysisExecutor;

    /**
     * 异步生成智能回复 (已弃用，建议直接调用generateSmartReply)
     */
    @Deprecated
    public void generateAndSendReplyAsync(Message message) {
        CompletableFuture.runAsync(() -> {
            try {
                String reply = generateSmartReply(message);
                if (reply != null) {
                    log.info("异步生成智能回复完成: messageId={}, reply={}", message.getId(), reply);
                }
            } catch (Exception e) {
                log.error("生成智能回复失败: messageId={}, error={}", message.getId(), e.getMessage(), e);
            }
        }, messageAnalysisExecutor);
    }

    /**
     * 生成并发送智能回复 (已移除SSE推送，改为直接返回回复内容)
     */
    public String generateSmartReply(Message message) {
        try {
            // 检查智能回复是否启用
            if (!smartReplyConfig.isEnabled()) {
                log.debug("智能回复功能已禁用");
                return null;
            }

            // 检查是否需要回复
            if (!shouldGenerateReply(message)) {
                log.info("消息不需要智能回复: messageId={}, category={}",
                        message.getId(), message.getCategory());
                return null;
            }

            // 获取用户最近的消息历史
            String userIdentifier = message.getUserIdentifier() != null ?
                message.getUserIdentifier() : "user_" + message.getUserId();
            List<String> recentMessages = getRecentMessageContents(userIdentifier, 5);

            // 生成智能回复
            String replyContent = deepSeekService.generateSmartReply(
                message.getContent(),
                message.getCategory(),
                recentMessages,
                message.getImportanceScore()
            );

            log.info("智能回复生成成功: messageId={}, reply={}", message.getId(), replyContent);
            return replyContent;

        } catch (Exception e) {
            log.error("生成智能回复过程中出错: messageId={}, error={}", 
                    message.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断是否需要生成智能回复
     */
    private boolean shouldGenerateReply(Message message) {
        // 如果AI分析失败，不回复
        if (message.getAiStatus() != Message.AiProcessStatus.COMPLETED) {
            return false;
        }

        // 如果是搜索或统计请求，不需要额外回复（已经在主流程中回复了）
        String content = message.getContent().toLowerCase();
        if (isSearchOrStatsRequest(content)) {
            return false;
        }

        // 根据消息类别决定是否回复
        MessageCategory category = message.getCategory();
        if (category == null) {
            return true; // 未分类的消息给予基本回复
        }

        // 使用配置的最低重要性阈值
        int minScore = smartReplyConfig.getStrategy().getMinImportanceScore();

        return switch (category) {
            case TODO, IMPORTANT_INFO -> true; // 重要消息一定回复
            case CONTACT_INFO, LEARNING_NOTE -> true; // 联系信息和学习相关回复
            case PERSONAL_THOUGHT -> message.getImportanceScore() >= Math.max(minScore, 7);
            case FINANCIAL -> true; // 财务信息回复确认
            case HEALTH -> true; // 健康相关给予关怀回复
            case ENTERTAINMENT -> message.getImportanceScore() >= minScore;
            default -> message.getImportanceScore() >= minScore; // 其他类型根据配置的重要性回复
        };
    }

    /**
     * 检查是否为搜索或统计请求
     */
    private boolean isSearchOrStatsRequest(String content) {
        String[] searchKeywords = {
            "搜索", "查找", "找", "search", "find",
            "什么时候", "哪里", "谁", "怎么", "为什么",
            "记录", "说过", "提到", "关于"
        };
        
        String[] statsKeywords = {
            "统计", "总结", "汇总", "数量", "多少",
            "stats", "summary", "count"
        };
        
        for (String keyword : searchKeywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        
        for (String keyword : statsKeywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        
        return content.endsWith("?") || content.endsWith("？");
    }

    /**
     * 获取用户最近的消息内容
     */
    private List<String> getRecentMessageContents(String userIdentifier, int limit) {
        try {
            return messageRepository.findByUserIdentifierOrderByCreatedAtDesc(userIdentifier)
                    .stream()
                    .limit(limit)
                    .map(msg -> {
                        String category = msg.getCategory() != null ?
                                msg.getCategory().getDisplayName() : "未分类";
                        return String.format("[%s] %s", category, msg.getContent());
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("获取用户历史消息失败: userIdentifier={}, error={}", userIdentifier, e.getMessage());
            return List.of();
        }
    }

    /**
     * 获取回复延迟时间（毫秒）
     * 根据消息重要性和类别调整回复时机
     */
    private long getReplyDelay(Message message) {
        MessageCategory category = message.getCategory();
        Integer importance = message.getImportanceScore();
        
        // 基础延迟时间（让用户感觉是经过思考的回复）
        long baseDelay = 2000; // 2秒
        
        if (category == MessageCategory.TODO || category == MessageCategory.IMPORTANT_INFO) {
            return baseDelay; // 重要消息快速回复
        }
        
        if (importance != null && importance >= 8) {
            return baseDelay; // 高重要性快速回复
        }
        
        if (importance != null && importance <= 3) {
            return baseDelay + 3000; // 低重要性延迟回复
        }
        
        return baseDelay + 1000; // 默认延迟
    }

    /**
     * 带延迟的异步回复 (已弃用，建议直接调用generateSmartReply)
     */
    @Deprecated
    public void generateAndSendReplyWithDelay(Message message) {
        long delay = getReplyDelay(message);

        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(delay);
                String reply = generateSmartReply(message);
                if (reply != null) {
                    log.info("延迟智能回复完成: messageId={}, delay={}ms, reply={}", 
                            message.getId(), delay, reply);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("智能回复延迟被中断: messageId={}", message.getId());
            } catch (Exception e) {
                log.error("延迟智能回复失败: messageId={}, error={}", message.getId(), e.getMessage(), e);
            }
        }, messageAnalysisExecutor);
    }
}
