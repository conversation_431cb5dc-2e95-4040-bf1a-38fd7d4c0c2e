#!/bin/bash

# 重新部署脚本 - 修复时区问题
echo "开始重新部署 Wedin AI Assistant 后端服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误：Docker 未运行或无权限访问"
    exit 1
fi

# 停止现有容器
echo "停止现有容器..."
docker-compose down

# 删除旧镜像（强制重新构建）
echo "删除旧镜像..."
docker rmi backend_wedin-backend 2>/dev/null || true
docker rmi wedin-backend_wedin-backend 2>/dev/null || true

# 清理未使用的镜像
echo "清理未使用的镜像..."
docker image prune -f

# 重新构建并启动
echo "重新构建并启动服务..."
if docker-compose up -d --build; then
    echo "服务启动成功"
else
    echo "服务启动失败，查看错误日志："
    docker-compose logs wedin-backend
    exit 1
fi

# 等待服务启动
echo "等待服务启动..."
sleep 15

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 检查容器健康状态
echo "检查容器健康状态..."
container_id=$(docker-compose ps -q wedin-backend)
if [ -n "$container_id" ]; then
    echo "容器ID: $container_id"
    echo "容器状态: $(docker inspect --format='{{.State.Status}}' $container_id)"
    echo "容器时区: $(docker exec $container_id date)"
fi

# 检查日志
echo "查看最新日志..."
docker-compose logs --tail=30 wedin-backend

echo "重新部署完成！"
echo "请检查日志中的时间是否正确显示为中国时间。"
