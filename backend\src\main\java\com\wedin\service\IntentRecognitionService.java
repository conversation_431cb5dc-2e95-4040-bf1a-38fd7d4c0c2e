package com.wedin.service;

import com.wedin.dto.DeepSeekRequest;
import com.wedin.dto.DeepSeekResponse;
import com.wedin.config.DeepSeekConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 意图识别服务
 * 用于判断用户消息是普通对话还是需要分类记录的内容
 */
@Service
@Slf4j
public class IntentRecognitionService {

    private final DeepSeekConfig deepSeekConfig;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public IntentRecognitionService(DeepSeekConfig deepSeekConfig, ObjectMapper objectMapper) {
        this.deepSeekConfig = deepSeekConfig;
        this.objectMapper = objectMapper;
        this.webClient = WebClient.builder()
                .baseUrl(deepSeekConfig.getBaseUrl())
                .defaultHeader("Authorization", "Bearer " + deepSeekConfig.getApiKey())
                .defaultHeader("Content-Type", "application/json")
                .build();
    }

    /**
     * 消息意图类型
     */
    public enum MessageIntent {
        /**
         * 普通对话 - 闲聊、问候、询问AI等
         */
        CASUAL_CHAT,
        
        /**
         * 需要分类记录 - 待办、笔记、重要信息等
         */
        RECORD_NEEDED
    }

    /**
     * 识别消息意图
     * @param userMessage 用户消息
     * @return 消息意图类型
     */
    public MessageIntent recognizeIntent(String userMessage) {
        try {
            // 直接使用AI进行意图识别，更准确且易维护
            return recognizeByAI(userMessage);
        } catch (Exception e) {
            log.error("意图识别失败，使用简单规则兜底: {}", e.getMessage(), e);
            // 出错时使用简单规则兜底
            return fallbackRecognition(userMessage);
        }
    }

    /**
     * 兜底的简单规则识别（仅在AI失败时使用）
     */
    private MessageIntent fallbackRecognition(String userMessage) {
        String content = userMessage.toLowerCase().trim();

        // 明确的记录指令
        String[] recordCommands = {"提醒我", "记录", "保存", "记住", "待办", "任务"};
        for (String command : recordCommands) {
            if (content.contains(command)) {
                return MessageIntent.RECORD_NEEDED;
            }
        }

        // 简单问候
        String[] greetings = {"你好", "hi", "hello", "谢谢", "再见"};
        for (String greeting : greetings) {
            if (content.equals(greeting)) {
                return MessageIntent.CASUAL_CHAT;
            }
        }

        // 默认：短句对话，长句记录
        return content.length() <= 10 ? MessageIntent.CASUAL_CHAT : MessageIntent.RECORD_NEEDED;
    }



    /**
     * 基于AI的意图识别
     */
    private MessageIntent recognizeByAI(String userMessage) {
        try {
            String prompt = buildIntentPrompt(userMessage);
            String response = callDeepSeekAPI(prompt);
            return parseIntentResponse(response);
        } catch (Exception e) {
            log.error("AI意图识别失败: {}", e.getMessage(), e);
            return MessageIntent.RECORD_NEEDED; // 默认为需要记录
        }
    }

    /**
     * 构建意图识别提示词
     */
    private String buildIntentPrompt(String userMessage) {
        return String.format("""
            请判断以下用户消息的意图类型，并以JSON格式返回：

            用户消息："%s"

            请判断这条消息是：
            1. CASUAL_CHAT：普通对话
            2. RECORD_NEEDED：需要分类记录

            ## 判断标准：

            ### CASUAL_CHAT（普通对话）包括：
            - 问候语：你好、早上好、再见等
            - 询问AI：你是谁、你能做什么等
            - 闲聊：开玩笑、聊天等
            - 一般性询问：天气怎么样、现在几点、明天下雨吗等
            - 简单回应：是的、好的、谢谢等
            - 情感表达：哈哈、有趣等

            ### RECORD_NEEDED（需要记录）包括：
            - 明确的记录指令：提醒我、记录、保存、记住等
            - 待办事项：明天要开会、需要买东西等
            - 重要信息：联系方式、地址、密码等
            - 学习笔记：知识点、心得体会等
            - 个人想法：想法、计划、目标等
            - 具体安排：时间安排、会议安排等

            ## 关键区别：
            - 询问信息（如"明天天气怎么样"）→ CASUAL_CHAT
            - 记录信息（如"明天要开会"）→ RECORD_NEEDED
            - 一般性问题 → CASUAL_CHAT
            - 具体事务安排 → RECORD_NEEDED

            请以以下JSON格式返回：
            {
                "intent": "CASUAL_CHAT 或 RECORD_NEEDED",
                "confidence": 置信度(0-1),
                "reason": "判断理由"
            }
            """, userMessage);
    }

    /**
     * 调用DeepSeek API
     */
    private String callDeepSeekAPI(String prompt) {
        DeepSeekRequest request = DeepSeekRequest.builder()
                .model(deepSeekConfig.getModel())
                .maxTokens(150) // 意图识别不需要太多token
                .temperature(0.1) // 较低温度确保一致性
                .stream(false)
                .messages(List.of(
                        DeepSeekRequest.ChatMessage.builder()
                                .role("user")
                                .content(prompt)
                                .build()
                ))
                .build();

        Mono<DeepSeekResponse> responseMono = webClient.post()
                .uri("/chat/completions")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(DeepSeekResponse.class);

        DeepSeekResponse response = responseMono.block();
        
        if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
            return response.getChoices().get(0).getMessage().getContent();
        }
        
        throw new RuntimeException("DeepSeek API调用失败");
    }

    /**
     * 解析AI的意图识别响应
     */
    private MessageIntent parseIntentResponse(String response) {
        try {
            // 提取JSON部分
            String jsonStr = extractJsonFromResponse(response);
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            
            String intent = jsonNode.path("intent").asText();
            double confidence = jsonNode.path("confidence").asDouble(0.5);
            String reason = jsonNode.path("reason").asText();
            
            log.info("AI意图识别结果: intent={}, confidence={}, reason={}", intent, confidence, reason);
            
            // 如果置信度太低，默认为需要记录
            if (confidence < 0.6) {
                log.warn("意图识别置信度过低: {}, 默认为需要记录", confidence);
                return MessageIntent.RECORD_NEEDED;
            }
            
            if ("CASUAL_CHAT".equals(intent)) {
                return MessageIntent.CASUAL_CHAT;
            } else {
                return MessageIntent.RECORD_NEEDED;
            }
        } catch (Exception e) {
            log.error("解析意图识别响应失败: {}", e.getMessage(), e);
            return MessageIntent.RECORD_NEEDED;
        }
    }

    /**
     * 从响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        if (response == null) {
            return "{}";
        }

        // 查找JSON开始和结束位置
        int start = response.indexOf('{');
        int end = response.lastIndexOf('}');

        if (start != -1 && end != -1 && end > start) {
            return response.substring(start, end + 1);
        }

        // 如果没有找到完整的JSON，尝试简单解析
        if (response.toLowerCase().contains("casual_chat")) {
            return "{\"intent\":\"CASUAL_CHAT\",\"confidence\":0.8}";
        } else {
            return "{\"intent\":\"RECORD_NEEDED\",\"confidence\":0.8}";
        }
    }
} 