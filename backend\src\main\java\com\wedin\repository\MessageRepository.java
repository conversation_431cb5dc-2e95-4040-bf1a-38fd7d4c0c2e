package com.wedin.repository;

import com.wedin.entity.Message;
import com.wedin.entity.MessageCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 消息数据访问层
 */
@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {

    /**
     * 根据用户标识查询消息
     */
    List<Message> findByUserIdentifierOrderByCreatedAtDesc(String userIdentifier);

    /**
     * 根据用户标识分页查询消息
     */
    Page<Message> findByUserIdentifierOrderByCreatedAtDesc(String userIdentifier, Pageable pageable);

    /**
     * 根据用户标识和分类查询消息
     */
    List<Message> findByUserIdentifierAndCategoryOrderByCreatedAtDesc(String userIdentifier, MessageCategory category);

    /**
     * 根据用户标识和时间范围查询消息
     */
    List<Message> findByUserIdentifierAndCreatedAtBetweenOrderByCreatedAtDesc(
            String userIdentifier, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户标识和时间查询指定时间之后的消息（分页）
     */
    Page<Message> findByUserIdentifierAndCreatedAtAfterOrderByCreatedAtDesc(
            String userIdentifier, LocalDateTime afterTime, Pageable pageable);

    /**
     * 根据用户标识查询需要提醒的消息
     */
    List<Message> findByUserIdentifierAndNeedReminderTrueAndReminderTimeBeforeOrderByReminderTimeAsc(
            String userIdentifier, LocalDateTime currentTime);

    /**
     * 全文搜索消息内容
     */
    @Query("SELECT m FROM Message m WHERE m.userIdentifier = :userIdentifier AND " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY m.createdAt DESC")
    List<Message> searchByKeyword(@Param("userIdentifier") String userIdentifier,
                                 @Param("keyword") String keyword);

    /**
     * 多关键词搜索
     */
    @Query("SELECT m FROM Message m WHERE m.userIdentifier = :userIdentifier AND " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :keyword1, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :keyword1, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :keyword1, '%'))) AND " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :keyword2, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :keyword2, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :keyword2, '%'))) " +
           "ORDER BY m.createdAt DESC")
    List<Message> searchByTwoKeywords(@Param("userIdentifier") String userIdentifier,
                                     @Param("keyword1") String keyword1,
                                     @Param("keyword2") String keyword2);

    /**
     * 根据重要性评分查询消息
     */
    List<Message> findByUserIdentifierAndImportanceScoreGreaterThanEqualOrderByImportanceScoreDescCreatedAtDesc(
            String userIdentifier, Integer minScore);

    /**
     * 统计用户消息数量
     */
    long countByUserIdentifier(String userIdentifier);

    /**
     * 统计用户各分类消息数量
     */
    @Query("SELECT m.category, COUNT(m) FROM Message m WHERE m.userIdentifier = :userIdentifier GROUP BY m.category")
    List<Object[]> countByUserIdentifierGroupByCategory(@Param("userIdentifier") String userIdentifier);

    // ========== PWA API 新增查询方法 ==========

    /**
     * 根据分类分页查询消息
     */
    Page<Message> findByCategoryOrderByCreatedAtDesc(MessageCategory category, Pageable pageable);

    /**
     * 搜索消息内容（分页）
     */
    @Query("SELECT m FROM Message m WHERE " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "ORDER BY m.createdAt DESC")
    Page<Message> searchByContent(@Param("query") String query, Pageable pageable);

    /**
     * 根据分类搜索消息（分页）
     */
    @Query("SELECT m FROM Message m WHERE m.category = :category AND " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "ORDER BY m.createdAt DESC")
    Page<Message> searchByCategory(@Param("query") String query,
                                  @Param("category") MessageCategory category,
                                  Pageable pageable);

    /**
     * 根据时间范围搜索消息（分页）
     */
    @Query("SELECT m FROM Message m WHERE m.createdAt BETWEEN :startDate AND :endDate AND " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "ORDER BY m.createdAt DESC")
    Page<Message> searchByDateRange(@Param("query") String query,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate,
                                   Pageable pageable);

    /**
     * 根据分类和时间范围搜索消息（分页）
     */
    @Query("SELECT m FROM Message m WHERE m.category = :category AND " +
           "m.createdAt BETWEEN :startDate AND :endDate AND " +
           "(LOWER(m.content) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.aiSummary) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(m.keywords) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "ORDER BY m.createdAt DESC")
    Page<Message> searchByCategoryAndDateRange(@Param("query") String query,
                                              @Param("category") MessageCategory category,
                                              @Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate,
                                              Pageable pageable);

    /**
     * 统计所有分类消息数量
     */
    @Query("SELECT m.category, COUNT(m) FROM Message m GROUP BY m.category")
    List<Object[]> countByCategory();

    /**
     * 查询有提醒的消息
     */
    List<Message> findByNeedReminderTrueOrderByReminderTimeAsc();
    
    /**
     * 查询指定时间之后创建的消息（分页）
     */
    Page<Message> findByCreatedAtAfterOrderByCreatedAtDesc(LocalDateTime afterTime, Pageable pageable);
}
