package com.wedin.service;

import com.wedin.config.TodoReminderConfig;
import com.wedin.entity.Message;
import com.wedin.entity.MessageCategory;
import com.wedin.entity.TodoReminder;
import com.wedin.repository.TodoReminderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 待办事项提醒服务
 * 通过极光推送发送推送通知
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TodoReminderService {

    private final TodoReminderRepository todoReminderRepository;
    private final TodoReminderConfig todoReminderConfig;
    private final JPushService jpushService;

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("M月d日 HH:mm");

    /**
     * 创建待办事项提醒
     */
    @Transactional
    public TodoReminder createTodoReminder(Message message) {
        log.info("尝试创建待办事项提醒: messageId={}, category={}, reminderTime={}",
                message.getId(), message.getCategory(), message.getReminderTime());

        if (message.getCategory() != MessageCategory.TODO) {
            log.info("消息不是待办事项类型，跳过创建提醒: messageId={}, category={}",
                    message.getId(), message.getCategory());
            return null;
        }

        if (message.getReminderTime() == null) {
            log.info("消息没有提醒时间，跳过创建提醒: messageId={}", message.getId());
            return null;
        }

        // 检查是否已存在提醒
        TodoReminder existingReminder = todoReminderRepository.findByMessageId(message.getId());
        if (existingReminder != null) {
            log.info("消息已存在提醒记录: messageId={}, reminderId={}", 
                    message.getId(), existingReminder.getId());
            return existingReminder;
        }

        // 计算提醒时间（根据配置提前提醒）
        LocalDateTime reminderTime = message.getReminderTime().minusMinutes(todoReminderConfig.getAdvanceMinutes());

        // 如果提醒时间已经过了，设置为立即提醒
        LocalDateTime now = LocalDateTime.now();
        if (reminderTime.isBefore(now)) {
            // 设置为下一个整分钟，确保能被定时任务扫描到
            reminderTime = now.withSecond(0).withNano(0).plusMinutes(1);
            log.info("提醒时间已过，调整为下一个整分钟: originalTime={}, adjustedTime={}",
                    message.getReminderTime().minusMinutes(todoReminderConfig.getAdvanceMinutes()), reminderTime);
        }

        TodoReminder reminder = TodoReminder.builder()
                .messageId(message.getId())
                .userOpenId(message.getUserIdentifier() != null ? message.getUserIdentifier() : "user_" + message.getUserId())
                .todoContent(message.getContent())
                .triggerTime(message.getReminderTime())
                .reminderTime(reminderTime)
                .status(TodoReminder.ReminderStatus.PENDING)
                .build();

        reminder = todoReminderRepository.save(reminder);
        
        log.info("创建待办事项提醒成功: messageId={}, reminderId={}, triggerTime={}, reminderTime={}", 
                message.getId(), reminder.getId(), 
                message.getReminderTime().format(TIME_FORMATTER),
                reminderTime.format(TIME_FORMATTER));

        return reminder;
    }

    /**
     * 定时扫描并发送提醒
     * 根据配置的间隔执行
     */
    @Scheduled(fixedRateString = "#{@todoReminderConfig.scanInterval}")
    public void scanAndSendReminders() {
        try {
            log.debug("定时任务执行: 扫描待办事项提醒, 当前时间: {}", LocalDateTime.now());

            // 检查功能是否启用
            if (!todoReminderConfig.isEnabled()) {
                log.debug("待办事项提醒功能已禁用，跳过扫描");
                return;
            }

            LocalDateTime now = LocalDateTime.now();
            List<TodoReminder> pendingReminders = todoReminderRepository.findPendingReminders(now);

            log.debug("扫描结果: 当前时间={}, 找到{}个待发送的提醒", now, pendingReminders.size());

            // 添加详细调试信息
            if (log.isDebugEnabled()) {
                List<TodoReminder> allPending = todoReminderRepository.findByStatus(TodoReminder.ReminderStatus.PENDING);
                log.debug("所有PENDING状态的提醒数量: {}", allPending.size());
                for (TodoReminder reminder : allPending) {
                    log.debug("PENDING提醒详情: id={}, reminderTime={}, triggerTime={}, content={}",
                            reminder.getId(), reminder.getReminderTime(), reminder.getTriggerTime(), reminder.getTodoContent());
                }
            }

            if (pendingReminders.isEmpty()) {
                return;
            }

            log.info("找到 {} 个待发送的提醒", pendingReminders.size());

            for (TodoReminder reminder : pendingReminders) {
                try {
                    log.info("处理提醒: reminderId={}, 提醒时间={}, 触发时间={}, 内容={}",
                            reminder.getId(), reminder.getReminderTime(),
                            reminder.getTriggerTime(), reminder.getTodoContent());
                    sendReminderMessage(reminder);
                } catch (Exception e) {
                    log.error("发送提醒失败: reminderId={}, error={}",
                            reminder.getId(), e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("扫描提醒任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送提醒消息（通过极光推送）
     */
    @Transactional
    public void sendReminderMessage(TodoReminder reminder) {
        try {
            log.info("开始发送待办事项提醒: reminderId={}, content={}",
                    reminder.getId(), reminder.getTodoContent());

            // 通过极光推送发送推送通知
            boolean jpushSent = false;
            try {
                jpushSent = jpushService.sendReminderNotification(reminder);
                if (jpushSent) {
                    log.info("待办事项提醒推送成功: reminderId={}, content={}",
                            reminder.getId(), reminder.getTodoContent());
                } else {
                    log.warn("待办事项提醒推送失败: reminderId={}, userId={} - 用户可能没有活跃的极光推送设备",
                            reminder.getId(), reminder.getUserOpenId());

                    // 检查用户是否有极光推送设备
                    long deviceCount = jpushService.getUserDeviceCount(reminder.getUserOpenId());
                    if (deviceCount == 0) {
                        log.info("用户 {} 没有活跃的极光推送设备，建议用户重新安装应用或检查推送权限", reminder.getUserOpenId());
                    }
                }
            } catch (Exception jpushError) {
                log.error("极光推送异常: reminderId={}, error={}",
                        reminder.getId(), jpushError.getMessage(), jpushError);
            }

            // 无论推送是否成功，都标记为已提醒，避免重复尝试
            reminder.setStatus(TodoReminder.ReminderStatus.REMINDED);
            reminder.setReminderCount(reminder.getReminderCount() + 1);
            reminder.setLastReminderTime(LocalDateTime.now());
            todoReminderRepository.save(reminder);

        } catch (Exception e) {
            log.error("发送待办事项提醒异常: reminderId={}, error={}",
                    reminder.getId(), e.getMessage(), e);
        }
    }





    /**
     * 标记待办事项为已完成
     */
    @Transactional
    public boolean markTodoCompleted(String userOpenId, String todoContent) {
        List<TodoReminder> reminders = todoReminderRepository.findByUserOpenIdAndStatusOrderByTriggerTimeAsc(
                userOpenId, TodoReminder.ReminderStatus.REMINDED);
        
        // 查找匹配的待办事项
        for (TodoReminder reminder : reminders) {
            if (reminder.getTodoContent().contains(todoContent) || 
                todoContent.contains(reminder.getTodoContent())) {
                
                reminder.setStatus(TodoReminder.ReminderStatus.COMPLETED);
                todoReminderRepository.save(reminder);
                
                log.info("待办事项标记为已完成: reminderId={}, content={}", 
                        reminder.getId(), reminder.getTodoContent());
                return true;
            }
        }
        
        return false;
    }

    /**
     * 根据ID查找提醒记录
     */
    public Optional<TodoReminder> findById(Long id) {
        return todoReminderRepository.findById(id);
    }

    /**
     * 获取当前需要提醒的待办事项（状态为REMINDED且刚刚被提醒的）
     */
    public String getCurrentReminders(String userOpenId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneHourAgo = now.minusHours(1);

        // 查找最近1小时内被标记为REMINDED的提醒
        List<TodoReminder> currentReminders = todoReminderRepository.findByUserOpenIdAndStatusOrderByTriggerTimeAsc(
                userOpenId, TodoReminder.ReminderStatus.REMINDED)
                .stream()
                .filter(reminder -> reminder.getLastReminderTime() != null &&
                                  reminder.getLastReminderTime().isAfter(oneHourAgo))
                .toList();

        if (currentReminders.isEmpty()) {
            return "📝 当前没有需要提醒的待办事项。";
        }

        StringBuilder result = new StringBuilder("⏰ 待办事项提醒：\n\n");

        for (TodoReminder reminder : currentReminders) {
            result.append("🔔 时间：").append(reminder.getTriggerTime().format(TIME_FORMATTER)).append("\n");
            result.append("📋 内容：").append(reminder.getTodoContent()).append("\n");
            result.append("⏱️ 提醒时间：").append(reminder.getLastReminderTime().format(TIME_FORMATTER)).append("\n\n");
            result.append("请及时处理哦！如已完成，可回复\"完成\"来标记。\n\n");
        }

        return result.toString();
    }

    /**
     * 获取用户的待办事项列表
     */
    public String getUserTodoList(String userOpenId) {
        List<TodoReminder> pendingTodos = todoReminderRepository.findByUserOpenIdAndStatusOrderByTriggerTimeAsc(
                userOpenId, TodoReminder.ReminderStatus.PENDING);

        List<TodoReminder> remindedTodos = todoReminderRepository.findByUserOpenIdAndStatusOrderByTriggerTimeAsc(
                userOpenId, TodoReminder.ReminderStatus.REMINDED);

        List<TodoReminder> completedTodos = todoReminderRepository.findByUserOpenIdAndStatusOrderByTriggerTimeAsc(
                userOpenId, TodoReminder.ReminderStatus.COMPLETED);

        if (pendingTodos.isEmpty() && remindedTodos.isEmpty() && completedTodos.isEmpty()) {
            return "📝 您目前没有待办事项。";
        }

        StringBuilder result = new StringBuilder("📝 您的待办事项：\n\n");

        // 显示待提醒的事项
        if (!pendingTodos.isEmpty()) {
            result.append("⏳ 待提醒：\n");
            for (TodoReminder todo : pendingTodos) {
                result.append("• ").append(todo.getTriggerTime().format(TIME_FORMATTER))
                      .append(" - ").append(todo.getTodoContent()).append("\n");
            }
            result.append("\n");
        }

        // 显示已提醒的事项
        if (!remindedTodos.isEmpty()) {
            result.append("🔔 已提醒：\n");
            for (TodoReminder todo : remindedTodos) {
                result.append("• ").append(todo.getTriggerTime().format(TIME_FORMATTER))
                      .append(" - ").append(todo.getTodoContent()).append("\n");
            }
            result.append("\n");
        }

        // 显示已完成的事项
        if (!completedTodos.isEmpty()) {
            result.append("✅ 已完成：\n");
            for (TodoReminder todo : completedTodos) {
                result.append("• ").append(todo.getTriggerTime().format(TIME_FORMATTER))
                      .append(" - ").append(todo.getTodoContent()).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 清理过期的提醒
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional
    public void cleanupExpiredReminders() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<TodoReminder> expiredReminders = todoReminderRepository.findExpiredReminders(now);
            
            for (TodoReminder reminder : expiredReminders) {
                // 如果触发时间已过24小时，标记为过期
                if (reminder.getTriggerTime().plusHours(24).isBefore(now)) {
                    reminder.setStatus(TodoReminder.ReminderStatus.EXPIRED);
                    todoReminderRepository.save(reminder);
                    
                    log.info("待办事项标记为过期: reminderId={}, triggerTime={}", 
                            reminder.getId(), reminder.getTriggerTime());
                }
            }
            
            log.info("清理过期提醒任务完成，处理了 {} 个过期提醒", expiredReminders.size());
            
        } catch (Exception e) {
            log.error("清理过期提醒任务失败: {}", e.getMessage(), e);
        }
    }
}
