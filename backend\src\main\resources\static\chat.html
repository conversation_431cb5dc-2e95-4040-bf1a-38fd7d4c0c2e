<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wedin - 智能聊天助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 1000px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 登录界面样式 */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 28px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 聊天界面样式 */
        .chat-container {
            display: none;
            height: 100%;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 20px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 24px 32px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
            gap: 14px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
        }

        .message-content {
            max-width: 60%;
            min-width: 120px;
            padding: 14px 18px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
            word-break: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }

        .message-status {
            font-size: 12px;
            margin-top: 4px;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
        }

        .status-processing {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .chat-input {
            padding: 24px 32px;
            background: white;
            border-top: 1px solid #e1e5e9;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            min-height: 60px;
            max-height: 120px;
            padding: 18px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 22px;
            resize: none;
            font-family: inherit;
            font-size: 16px;
            line-height: 1.5;
            transition: border-color 0.3s;
            overflow-y: hidden;
        }

        .message-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                height: 100vh;
                border-radius: 0;
                max-width: 100%;
            }

            .login-form {
                margin: 20px;
                padding: 30px;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-messages {
                padding: 16px 20px;
            }

            .chat-input {
                padding: 16px 20px;
            }

            .message {
                margin-bottom: 16px;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录界面 -->
        <div class="login-container" id="loginContainer">
            <form class="login-form" id="loginForm">
                <h1 class="login-title">Wedin 智能助手</h1>
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="login-btn" id="loginBtn">
                    <span id="loginBtnText">登录</span>
                    <span id="loginLoading" class="loading" style="display: none;"></span>
                </button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>

        <!-- 聊天界面 -->
        <div class="chat-container" id="chatContainer">
            <div class="chat-header">
                <div class="chat-title">智能聊天助手</div>
                <div class="user-info">
                    <span id="userDisplayName">用户</span>
                    <button class="logout-btn" id="logoutBtn">退出登录</button>
                </div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message ai">
                    <div class="message-avatar">AI</div>
                    <div>
                        <div class="message-content">
                            你好！我是Wedin智能助手，可以帮你记录和整理各种信息。请告诉我你想要记录什么内容吧！
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
            </div>
            <div class="chat-input">
                <div class="input-container">
                    <textarea 
                        class="message-input" 
                        id="messageInput" 
                        placeholder="输入你想要记录的信息..."
                        rows="1"></textarea>
                    <button class="send-btn" id="sendBtn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.token = localStorage.getItem('wedin_token');
                this.user = null;
                this.init();
            }

            init() {
                this.bindEvents();
                if (this.token) {
                    this.showChatInterface();
                } else {
                    this.showLoginInterface();
                }
            }

            bindEvents() {
                // 登录表单事件
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // 退出登录事件
                document.getElementById('logoutBtn').addEventListener('click', () => {
                    this.handleLogout();
                });

                // 发送消息事件
                document.getElementById('sendBtn').addEventListener('click', () => {
                    this.sendMessage();
                });

                // 输入框回车发送
                document.getElementById('messageInput').addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 自动调整输入框高度
                document.getElementById('messageInput').addEventListener('input', (e) => {
                    this.autoResizeTextarea(e.target);
                });
            }

            async handleLogin() {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const loginBtn = document.getElementById('loginBtn');
                const loginBtnText = document.getElementById('loginBtnText');
                const loginLoading = document.getElementById('loginLoading');
                const loginError = document.getElementById('loginError');

                // 显示加载状态
                loginBtn.disabled = true;
                loginBtnText.style.display = 'none';
                loginLoading.style.display = 'inline-block';
                loginError.textContent = '';

                try {
                    const response = await fetch('/wedin/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const result = await response.json();

                    if (result.code === 200) {
                        this.token = result.data.accessToken;
                        this.user = result.data.userInfo;
                        localStorage.setItem('wedin_token', this.token);
                        localStorage.setItem('wedin_user', JSON.stringify(this.user));
                        this.showChatInterface();
                    } else {
                        loginError.textContent = result.message || '登录失败';
                    }
                } catch (error) {
                    console.error('登录错误:', error);
                    loginError.textContent = '网络错误，请稍后重试';
                } finally {
                    // 恢复按钮状态
                    loginBtn.disabled = false;
                    loginBtnText.style.display = 'inline';
                    loginLoading.style.display = 'none';
                }
            }

            handleLogout() {
                localStorage.removeItem('wedin_token');
                localStorage.removeItem('wedin_user');
                this.token = null;
                this.user = null;
                this.showLoginInterface();
            }

            showLoginInterface() {
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('chatContainer').style.display = 'none';
            }

            showChatInterface() {
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('chatContainer').style.display = 'flex';

                // 显示用户信息
                if (!this.user) {
                    this.user = JSON.parse(localStorage.getItem('wedin_user') || '{}');
                }
                document.getElementById('userDisplayName').textContent = this.user.nickname || this.user.username || '用户';

                // 加载历史聊天记录
                this.loadChatHistory();
            }

            async sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const sendBtn = document.getElementById('sendBtn');
                const content = messageInput.value.trim();

                if (!content) return;

                // 禁用发送按钮
                sendBtn.disabled = true;
                messageInput.disabled = true;

                // 添加用户消息到界面
                this.addMessage('user', content);
                messageInput.value = '';
                this.autoResizeTextarea(messageInput);

                try {
                    const response = await fetch('/wedin/api/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: JSON.stringify({
                            content: content,
                            type: 'TEXT'
                        })
                    });

                    const result = await response.json();

                    if (result.code === 200) {
                        const message = result.data;
                        // 添加AI处理状态消息
                        const aiMessageId = this.addMessage('ai', '正在分析您的消息...', 'processing');
                        
                        // 轮询检查消息处理状态
                        this.pollMessageStatus(message.id, aiMessageId);
                    } else {
                        this.addMessage('ai', `发送失败: ${result.message}`, 'failed');
                    }
                } catch (error) {
                    console.error('发送消息错误:', error);
                    this.addMessage('ai', '网络错误，请稍后重试', 'failed');
                } finally {
                    // 恢复发送按钮
                    sendBtn.disabled = false;
                    messageInput.disabled = false;
                    messageInput.focus();
                }
            }

            async pollMessageStatus(messageId, aiMessageId) {
                try {
                    const response = await fetch(`/wedin/api/messages/${messageId}/status`, {
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    const result = await response.json();

                    if (result.code === 200) {
                        const message = result.data;

                        if (message.aiStatus === 'COMPLETED') {
                            // 检查是否有AI回复消息
                            await this.checkForAIReply(messageId, aiMessageId, message);
                        } else if (message.aiStatus === 'FAILED') {
                            this.updateMessage(aiMessageId, '处理失败，请稍后重试', 'failed');
                        } else {
                            // 继续轮询
                            setTimeout(() => this.pollMessageStatus(messageId, aiMessageId), 2000);
                        }
                    }
                } catch (error) {
                    console.error('查询消息状态错误:', error);
                    this.updateMessage(aiMessageId, '查询状态失败', 'failed');
                }
            }

            async checkForAIReply(messageId, aiMessageId, message) {
                try {
                    // 查询是否有AI回复消息
                    const response = await fetch(`/wedin/api/messages?page=1&size=10`, {
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    const result = await response.json();
                    if (result.code === 200) {
                        // 查找对应的AI回复消息
                        const aiReply = result.data.content.find(msg =>
                            msg.parentMessageId === messageId &&
                            (msg.type === 'AI_REPLY' || msg.type === 'AUTO_REPLY') &&
                            msg.isFromAI === true
                        );

                        if (aiReply && aiReply.content) {
                            // 如果找到AI回复，显示回复内容
                            this.updateMessage(aiMessageId, aiReply.content, 'completed');
                        } else {
                            // 如果没有找到AI回复，显示分析结果
                            this.updateMessage(aiMessageId, this.formatAIResponse(message), 'completed');
                        }
                    } else {
                        // 如果查询失败，显示分析结果
                        this.updateMessage(aiMessageId, this.formatAIResponse(message), 'completed');
                    }
                } catch (error) {
                    console.error('查询AI回复失败:', error);
                    // 如果查询失败，显示分析结果
                    this.updateMessage(aiMessageId, this.formatAIResponse(message), 'completed');
                }
            }

            formatAIResponse(message) {
                // 简化AI回复格式，类似App端
                let response = '';
                
                if (message.aiSummary) {
                    response += `📝 **摘要**: ${message.aiSummary}`;
                }
                
                if (message.keywords) {
                    if (response) response += '\n\n';
                    response += `🔑 **关键词**: ${message.keywords}`;
                }
                
                if (message.importanceScore) {
                    if (response) response += '\n\n';
                    response += `⭐ **重要性**: ${message.importanceScore}/10`;
                }
                
                if (response) response += '\n\n';
                response += '✅ 信息已成功记录和分析！';
                
                return response;
            }

            async loadChatHistory() {
                try {
                    // 清空当前消息（除了欢迎消息）
                    const messagesContainer = document.getElementById('chatMessages');
                    messagesContainer.innerHTML = '';

                    // 添加欢迎消息
                    this.addWelcomeMessage();

                    // 获取历史聊天记录
                    const response = await fetch('/wedin/api/messages/sync?page=1&size=50', {
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200 && result.data.content.length > 0) {
                            // 按时间顺序显示消息（最早的在上面）
                            const messages = result.data.content.reverse();

                            for (const message of messages) {
                                // 添加用户消息
                                if (message.content && message.content.trim()) {
                                    this.addMessage('user', message.content, 'completed', message.createdAt);

                                    // 查找对应的AI回复
                                    await this.loadAIReply(message.id);
                                }
                            }

                            // 滚动到底部
                            this.scrollToBottom();
                        }
                    }
                } catch (error) {
                    console.error('加载聊天历史失败:', error);
                }
            }

            async loadAIReply(messageId) {
                try {
                    // 查询是否有AI回复
                    const response = await fetch(`/wedin/api/messages/sync?page=1&size=100`, {
                        headers: {
                            'Authorization': `Bearer ${this.token}`
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200) {
                            // 查找对应的AI回复消息
                            const aiReply = result.data.content.find(msg =>
                                msg.parentMessageId === messageId &&
                                (msg.type === 'AI_REPLY' || msg.type === 'AUTO_REPLY') &&
                                msg.isFromAI === true
                            );

                            if (aiReply && aiReply.content) {
                                this.addMessage('ai', aiReply.content, 'completed', aiReply.createdAt);
                            } else {
                                // 如果没有找到AI回复，查询原消息的分析结果
                                const originalResponse = await fetch(`/wedin/api/messages/${messageId}`, {
                                    headers: {
                                        'Authorization': `Bearer ${this.token}`
                                    }
                                });

                                if (originalResponse.ok) {
                                    const originalResult = await originalResponse.json();
                                    if (originalResult.code === 200) {
                                        const originalMessage = originalResult.data;
                                        if (originalMessage.aiStatus === 'COMPLETED') {
                                            const aiResponse = this.formatAIResponse(originalMessage);
                                            this.addMessage('ai', aiResponse, 'completed', originalMessage.updatedAt);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('加载AI回复失败:', error);
                }
            }

            addWelcomeMessage() {
                const messagesContainer = document.getElementById('chatMessages');
                const welcomeDiv = document.createElement('div');
                welcomeDiv.className = 'message ai';
                welcomeDiv.innerHTML = `
                    <div class="message-avatar">AI</div>
                    <div>
                        <div class="message-content">
                            你好！我是Wedin智能助手，可以帮你记录和整理各种信息。请告诉我你想要记录什么内容吧！
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                `;
                messagesContainer.appendChild(welcomeDiv);
            }

            addMessage(type, content, status = null, timestamp = null) {
                const messagesContainer = document.getElementById('chatMessages');
                const messageId = 'msg_' + Date.now();
                
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.id = messageId;
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = type === 'user' ? (this.user?.nickname?.[0] || this.user?.username?.[0] || 'U') : 'AI';
                
                const contentWrapper = document.createElement('div');
                
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.innerHTML = this.formatMessageContent(content);
                
                const messageTime = document.createElement('div');
                messageTime.className = 'message-time';
                // 如果提供了时间戳，使用它；否则使用当前时间
                const displayTime = timestamp ? new Date(timestamp) : new Date();
                messageTime.textContent = this.formatTime(displayTime);
                
                contentWrapper.appendChild(messageContent);
                contentWrapper.appendChild(messageTime);
                
                if (status) {
                    const statusDiv = document.createElement('div');
                    statusDiv.className = `message-status status-${status}`;
                    statusDiv.textContent = this.getStatusText(status);
                    contentWrapper.appendChild(statusDiv);
                }
                
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(contentWrapper);
                
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                
                return messageId;
            }

            updateMessage(messageId, content, status = null) {
                const messageDiv = document.getElementById(messageId);
                if (!messageDiv) return;
                
                const messageContent = messageDiv.querySelector('.message-content');
                const statusDiv = messageDiv.querySelector('.message-status');
                
                if (messageContent) {
                    messageContent.innerHTML = this.formatMessageContent(content);
                }
                
                if (statusDiv && status) {
                    statusDiv.className = `message-status status-${status}`;
                    statusDiv.textContent = this.getStatusText(status);
                }
            }

            formatMessageContent(content) {
                // 简单的markdown格式化
                return content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\n/g, '<br>');
            }

            getStatusText(status) {
                const statusTexts = {
                    'processing': '处理中...',
                    'completed': '已完成',
                    'failed': '处理失败'
                };
                return statusTexts[status] || status;
            }

            autoResizeTextarea(textarea) {
                textarea.style.height = '60px';
                const newHeight = Math.max(Math.min(textarea.scrollHeight, 120), 60);
                textarea.style.height = newHeight + 'px';
            }

            formatTime(date) {
                const now = new Date();
                const diffMs = now - date;
                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                if (diffDays === 0) {
                    // 今天，显示时间
                    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays === 1) {
                    // 昨天
                    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays < 7) {
                    // 一周内，显示星期
                    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                    return weekdays[date.getDay()] + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else {
                    // 超过一周，显示日期
                    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }) + ' ' +
                           date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                }
            }

            scrollToBottom() {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>