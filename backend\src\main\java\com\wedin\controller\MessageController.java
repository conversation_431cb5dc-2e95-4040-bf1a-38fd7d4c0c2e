package com.wedin.controller;

import com.wedin.dto.ApiResponse;
import com.wedin.entity.Message;
import com.wedin.entity.MessageCategory;
import com.wedin.entity.User;
import com.wedin.service.MessageService;
import com.wedin.util.DateTimeParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/messages")
@PreAuthorize("isAuthenticated()")
@Tag(name = "消息管理", description = "消息发送和管理相关接口")
@SecurityRequirement(name = "bearerAuth")
@Slf4j
public class MessageController {

    @Autowired
    private MessageService messageService;

    /**
     * 发送消息
     */
    @Operation(summary = "发送消息", description = "发送新消息给AI助手")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "消息发送成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "消息发送失败"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "未认证")
    })
    @PostMapping
    public ResponseEntity<ApiResponse<Message>> sendMessage(@RequestBody MessageRequest request) {
        try {
            // 从JWT token中获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = (User) authentication.getPrincipal();

            // 创建消息对象
            Message message = new Message();
            message.setContent(request.getContent());
            message.setType(request.getType());
            message.setUserId(currentUser.getId());
            message.setUserIdentifier(currentUser.getUsername());

            // 处理消息（AI分析、分类等）
            Message savedMessage = messageService.processAndSaveMessage(message);

            return ResponseEntity.ok(new ApiResponse<>(200, "消息发送成功", savedMessage));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "消息发送失败: " + e.getMessage(), null));
        }
    }

    /**
     * 查询消息处理状态
     */
    @GetMapping("/{id}/status")
    public ResponseEntity<ApiResponse<Message>> getMessageStatus(@PathVariable Long id) {
        try {
            Optional<Message> messageOpt = messageService.findById(id);
            if (messageOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(new ApiResponse<>(200, "查询成功", messageOpt.get()));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "查询失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取消息列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponse<Message>>> getMessages(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) MessageCategory category) {

        try {
            // 从JWT token中获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = (User) authentication.getPrincipal();
            String userIdentifier = currentUser.getUsername();

            Pageable pageable = PageRequest.of(page - 1, size,
                    Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<Message> messagePage;
            if (category != null) {
                messagePage = messageService.findByUserIdentifierAndCategory(userIdentifier, category, pageable);
            } else {
                messagePage = messageService.findUserMessages(userIdentifier, pageable);
            }

            PageResponse<Message> response = new PageResponse<>(
                    messagePage.getContent(),
                    messagePage.getTotalElements(),
                    page,
                    size,
                    messagePage.getTotalPages()
            );

            return ResponseEntity.ok(new ApiResponse<>(200, "获取成功", response));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "获取失败: " + e.getMessage(), null));
        }
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<PageResponse<Message>>> searchMessages(
            @RequestParam String query,
            @RequestParam(required = false) MessageCategory category,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page - 1, size, 
                    Sort.by(Sort.Direction.DESC, "createdAt"));
            
            Page<Message> messagePage = messageService.searchMessages(
                    query, category, startDate, endDate, pageable);
            
            PageResponse<Message> response = new PageResponse<>(
                    messagePage.getContent(),
                    messagePage.getTotalElements(),
                    page,
                    size,
                    messagePage.getTotalPages()
            );
            
            return ResponseEntity.ok(new ApiResponse<>(200, "搜索成功", response));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "搜索失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取消息详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Message>> getMessageById(@PathVariable Long id) {
        try {
            Optional<Message> message = messageService.findById(id);
            if (message.isPresent()) {
                return ResponseEntity.ok(new ApiResponse<>(200, "获取成功", message.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "获取失败: " + e.getMessage(), null));
        }
    }

    /**
     * 更新消息
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Message>> updateMessage(
            @PathVariable Long id, @RequestBody MessageUpdateRequest request) {
        try {
            Optional<Message> existingMessage = messageService.findById(id);
            if (!existingMessage.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            Message message = existingMessage.get();
            if (request.getContent() != null) {
                message.setContent(request.getContent());
            }
            if (request.getCategory() != null) {
                message.setCategory(request.getCategory());
            }
            if (request.getImportance() != null) {
                message.setImportance(request.getImportance());
            }
            if (request.getReminderTime() != null) {
                LocalDateTime reminderTime = DateTimeParser.parseChineseTimeExpression(
                    request.getReminderTime(), LocalDateTime.now());
                message.setReminderTime(reminderTime);
            }
            if (request.getTags() != null) {
                message.setTags(request.getTags());
            }
            
            Message updatedMessage = messageService.save(message);
            return ResponseEntity.ok(new ApiResponse<>(200, "更新成功", updatedMessage));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "更新失败: " + e.getMessage(), null));
        }
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteMessage(@PathVariable Long id) {
        try {
            if (!messageService.existsById(id)) {
                return ResponseEntity.notFound().build();
            }
            
            messageService.deleteById(id);
            return ResponseEntity.ok(new ApiResponse<>(200, "删除成功", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "删除失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取分类统计
     */
    @GetMapping("/stats/category")
    public ResponseEntity<ApiResponse<Map<String, Long>>> getCategoryStats() {
        try {
            Map<String, Long> stats = messageService.getCategoryStats();
            return ResponseEntity.ok(new ApiResponse<>(200, "获取成功", stats));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "获取失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取提醒列表
     */
    @GetMapping("/reminders")
    public ResponseEntity<ApiResponse<List<Message>>> getReminders() {
        try {
            List<Message> reminders = messageService.findMessagesWithReminders();
            return ResponseEntity.ok(new ApiResponse<>(200, "获取成功", reminders));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "获取失败: " + e.getMessage(), null));
        }
    }

    /**
     * 确认AI理解结果
     */
    @PostMapping("/{id}/confirm")
    public ResponseEntity<ApiResponse<Message>> confirmMessage(@PathVariable Long id) {
        try {
            Message confirmedMessage = messageService.confirmMessage(id);
            return ResponseEntity.ok(new ApiResponse<>(200, "确认成功", confirmedMessage));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "确认失败: " + e.getMessage(), null));
        }
    }

    /**
     * 拒绝AI理解结果
     */
    @PostMapping("/{id}/reject")
    public ResponseEntity<ApiResponse<Message>> rejectMessage(
            @PathVariable Long id,
            @RequestBody RejectRequest request) {
        try {
            Message rejectedMessage = messageService.rejectMessage(id, request.getReason());
            return ResponseEntity.ok(new ApiResponse<>(200, "已拒绝，请重新说明", rejectedMessage));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "拒绝失败: " + e.getMessage(), null));
        }
    }

    /**
     * 修改AI理解结果
     */
    @PostMapping("/{id}/modify")
    public ResponseEntity<ApiResponse<Message>> modifyMessage(
            @PathVariable Long id,
            @RequestBody ModifyRequest request) {
        try {
            Message modifiedMessage = messageService.modifyMessage(id, request);
            return ResponseEntity.ok(new ApiResponse<>(200, "修改成功", modifiedMessage));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "修改失败: " + e.getMessage(), null));
        }
    }

    /**
     * 同步消息接口 - 用于Android客户端数据同步
     */
    @GetMapping("/sync")
    public ResponseEntity<ApiResponse<PageResponse<Message>>> syncMessages(
            @RequestParam(required = false) String lastSyncTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "50") int size) {

        try {
            // 从JWT token中获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = (User) authentication.getPrincipal();
            String userIdentifier = currentUser.getUsername();

            log.info("同步消息请求: userIdentifier={}, lastSyncTime={}, page={}, size={}",
                    userIdentifier, lastSyncTime, page, size);

            Pageable pageable = PageRequest.of(page - 1, size,
                    Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<Message> messagePage;

            if (lastSyncTime != null && !lastSyncTime.isEmpty()) {
                // 如果提供了同步时间，获取该时间之后的用户消息
                try {
                    LocalDateTime syncTime = DateTimeParser.parseISODateTime(lastSyncTime);
                    messagePage = messageService.findUserMessagesAfterTime(userIdentifier, syncTime, pageable);
                } catch (Exception e) {
                    // 如果时间解析失败，返回用户的所有消息
                    messagePage = messageService.findUserMessages(userIdentifier, pageable);
                }
            } else {
                // 如果没有提供同步时间，返回用户的所有消息
                messagePage = messageService.findUserMessages(userIdentifier, pageable);
            }
            
            PageResponse<Message> response = new PageResponse<>(
                    messagePage.getContent(),
                    messagePage.getTotalElements(),
                    page,
                    size,
                    messagePage.getTotalPages()
            );

            log.info("同步消息响应: userIdentifier={}, 返回消息数量={}, 总数={}",
                    userIdentifier, messagePage.getContent().size(), messagePage.getTotalElements());

            // 打印前几条消息的详细信息
            messagePage.getContent().stream().limit(3).forEach(msg ->
                log.info("消息详情: ID={}, userIdentifier={}, content={}, isFromAI={}, createdAt={}",
                        msg.getId(), msg.getUserIdentifier(), msg.getContent().substring(0, Math.min(20, msg.getContent().length())),
                        msg.getIsFromAI(), msg.getCreatedAt())
            );

            return ResponseEntity.ok(new ApiResponse<>(200, "同步成功", response));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(400, "同步失败: " + e.getMessage(), null));
        }
    }

    // 内部类定义
        public static class MessageRequest {
        private String content;
        private String type = "TEXT";
        private String userIdentifier;
        
        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getUserIdentifier() { return userIdentifier; }
        public void setUserIdentifier(String userIdentifier) { this.userIdentifier = userIdentifier; }
    }

    public static class MessageUpdateRequest {
        private String content;
        private MessageCategory category;
        private Integer importance;
        private String reminderTime;
        private String tags;

        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public MessageCategory getCategory() { return category; }
        public void setCategory(MessageCategory category) { this.category = category; }
        public Integer getImportance() { return importance; }
        public void setImportance(Integer importance) { this.importance = importance; }
        public String getReminderTime() { return reminderTime; }
        public void setReminderTime(String reminderTime) { this.reminderTime = reminderTime; }
        public String getTags() { return tags; }
        public void setTags(String tags) { this.tags = tags; }
    }

    public static class RejectRequest {
        private String reason;

        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class ModifyRequest {
        private MessageCategory category;
        private Integer importance;
        private String note;

        public MessageCategory getCategory() { return category; }
        public void setCategory(MessageCategory category) { this.category = category; }
        public Integer getImportance() { return importance; }
        public void setImportance(Integer importance) { this.importance = importance; }
        public String getNote() { return note; }
        public void setNote(String note) { this.note = note; }
    }

    public static class ApiResponse<T> {
        private int code;
        private String message;
        private T data;

        public ApiResponse(int code, String message, T data) {
            this.code = code;
            this.message = message;
            this.data = data;
        }

        // Getters and Setters
        public int getCode() { return code; }
        public void setCode(int code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }

    public static class PageResponse<T> {
        private List<T> content;
        private long total;
        private int page;
        private int size;
        private int totalPages;

        public PageResponse(List<T> content, long total, int page, int size, int totalPages) {
            this.content = content;
            this.total = total;
            this.page = page;
            this.size = size;
            this.totalPages = totalPages;
        }

        // Getters and Setters
        public List<T> getContent() { return content; }
        public void setContent(List<T> content) { this.content = content; }
        public long getTotal() { return total; }
        public void setTotal(long total) { this.total = total; }
        public int getPage() { return page; }
        public void setPage(int page) { this.page = page; }
        public int getSize() { return size; }
        public void setSize(int size) { this.size = size; }
        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
    }
}
