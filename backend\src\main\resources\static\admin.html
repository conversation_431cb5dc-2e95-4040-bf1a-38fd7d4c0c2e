<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WedIn AI助手 - 管理面板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        h1 {
            color: #333;
            margin: 0;
        }
        .auth-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="text"], input[type="password"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .user-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 WedIn AI助手 - 管理面板</h1>
            <div>
                <button onclick="openSwaggerDocs()">📖 API文档</button>
                <button onclick="logout()" class="btn-danger">登出</button>
            </div>
        </div>

        <!-- 认证状态 -->
        <div class="auth-section" id="authSection">
            <h3>🔐 认证状态</h3>
            <div id="authStatus" class="status info">检查认证状态中...</div>
            <div id="userInfo" class="user-info" style="display: none;"></div>
            
            <!-- 登录表单 -->
            <div id="loginForm" style="display: none;">
                <h4>请先登录</h4>
                <input type="text" id="username" placeholder="用户名" value="">
                <input type="password" id="password" placeholder="密码" value="">
                <button onclick="login()">登录</button>
            </div>
        </div>

        <!-- 管理功能区域 -->
        <div id="adminPanel" style="display: none;">
            <div class="grid">
                <!-- 系统状态 -->
                <div class="section">
                    <h2>📊 系统状态</h2>
                    <button onclick="checkSystemHealth()">检查系统健康</button>
                    <button onclick="getSystemStats()">获取统计信息</button>
                    <div id="systemStatus" class="status" style="display: none;"></div>
                </div>

                <!-- 推送管理 -->
                <div class="section">
                    <h2>🔔 推送管理</h2>
                    <input type="text" id="testUserId" value="gujiangfei" placeholder="测试用户ID">
                    <br>
                    <button onclick="checkPushStatus()">检查推送状态</button>
                    <button onclick="testPushNotification()">发送测试推送</button>
                    <button onclick="checkUserPushAvailability()">检查用户推送可用性</button>
                    <div id="pushStatus" class="status" style="display: none;"></div>
                </div>
            </div>

            <!-- 设备管理 -->
            <div class="section">
                <h2>📱 设备管理</h2>
                <input type="text" id="deviceUserId" value="gujiangfei" placeholder="用户ID">
                <button onclick="checkDeviceInfo()">查看设备信息</button>
                <div id="deviceInfo" class="status" style="display: none;"></div>
            </div>

            <!-- 操作日志 -->
            <div class="section">
                <h2>📝 操作日志</h2>
                <button onclick="clearLog()">清空日志</button>
                <div id="log" class="log"></div>
            </div>
        </div>
    </div>

    <script>
        const baseUrl = '/wedin/api';
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;

        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
        });

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        // 认证相关函数
        async function checkAuthStatus() {
            if (!authToken) {
                showLoginForm();
                return;
            }

            try {
                // 尝试访问一个需要认证的接口来验证token
                const response = await fetch(`${baseUrl}/admin/health`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    showAdminPanel();
                    showStatus('authStatus', '✅ 已认证', 'success');
                    log('认证状态检查通过');
                } else {
                    throw new Error('认证失败');
                }
            } catch (error) {
                log(`认证检查失败: ${error.message}`);
                authToken = null;
                localStorage.removeItem('authToken');
                showLoginForm();
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showStatus('authStatus', '请输入用户名和密码', 'error');
                return;
            }

            try {
                log(`尝试登录: ${username}`);
                const response = await fetch(`${baseUrl}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok && data.code === 200) {
                    authToken = data.data.accessToken;
                    currentUser = data.data.userInfo;
                    localStorage.setItem('authToken', authToken);
                    
                    showAdminPanel();
                    showStatus('authStatus', '✅ 登录成功', 'success');
                    log(`登录成功: ${currentUser.username}`);
                } else {
                    throw new Error(data.message || '登录失败');
                }
            } catch (error) {
                log(`登录失败: ${error.message}`);
                showStatus('authStatus', `❌ 登录失败: ${error.message}`, 'error');
            }
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            showLoginForm();
            log('已登出');
        }

        function showLoginForm() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('adminPanel').style.display = 'none';
            document.getElementById('userInfo').style.display = 'none';
            showStatus('authStatus', '⚠️ 需要登录', 'warning');
        }

        function showAdminPanel() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('adminPanel').style.display = 'block';
            
            if (currentUser) {
                const userInfoDiv = document.getElementById('userInfo');
                userInfoDiv.innerHTML = `
                    <strong>当前用户:</strong> ${currentUser.username} (${currentUser.nickname || currentUser.username})<br>
                    <strong>角色:</strong> ${currentUser.role}
                `;
                userInfoDiv.style.display = 'block';
            }
        }

        // API调用函数（需要认证）
        async function apiCall(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };

            const response = await fetch(url, { ...options, ...defaultOptions });
            
            if (response.status === 401) {
                logout();
                throw new Error('认证已过期，请重新登录');
            }

            return response;
        }

        // 系统管理函数
        async function checkSystemHealth() {
            log('检查系统健康状态...');
            try {
                const response = await apiCall(`${baseUrl}/admin/health`);
                const data = await response.json();

                let message = '<strong>系统健康状态:</strong><br>';
                message += `整体状态: ${data.data.overall ? '✅ 健康' : '❌ 异常'}<br>`;
                message += `推送服务: ${data.data.pushService ? '✅ 正常' : '❌ 异常'}<br>`;
                message += `数据库: ${data.data.database ? '✅ 正常' : '❌ 异常'}<br>`;
                message += `检查时间: ${data.data.checkTime}`;

                const statusType = data.data.overall ? 'success' : 'error';
                showStatus('systemStatus', message, statusType);
                log('系统健康检查完成');
            } catch (error) {
                log(`系统健康检查失败: ${error.message}`);
                showStatus('systemStatus', `检查失败: ${error.message}`, 'error');
            }
        }

        async function getSystemStats() {
            log('获取系统统计信息...');
            try {
                const response = await apiCall(`${baseUrl}/admin/stats`);
                const data = await response.json();

                let message = '<strong>系统统计信息:</strong><br>';
                message += `获取时间: ${data.data.timestamp}<br>`;
                message += `状态: ${data.message}`;

                showStatus('systemStatus', message, 'info');
                log('系统统计信息获取完成');
            } catch (error) {
                log(`获取统计信息失败: ${error.message}`);
                showStatus('systemStatus', `获取失败: ${error.message}`, 'error');
            }
        }

        async function checkPushStatus() {
            log('检查推送服务状态...');
            try {
                const response = await apiCall(`${baseUrl}/admin/push/status`);
                const data = await response.json();

                let message = '<strong>推送服务状态:</strong><br>';
                message += `服务状态: ${data.data.serviceAvailable ? '✅ 可用' : '❌ 不可用'}<br>`;
                message += `服务名称: ${data.data.serviceName}<br>`;
                message += `检查时间: ${data.data.checkTime}<br>`;
                message += `说明: ${data.data.message}`;

                const statusType = data.data.serviceAvailable ? 'success' : 'error';
                showStatus('pushStatus', message, statusType);
                log('推送状态检查完成');
            } catch (error) {
                log(`推送状态检查失败: ${error.message}`);
                showStatus('pushStatus', `检查失败: ${error.message}`, 'error');
            }
        }

        async function testPushNotification() {
            const userId = document.getElementById('testUserId').value || 'android_user_default';
            log(`发送测试推送到用户: ${userId}`);
            
            try {
                const response = await apiCall(`${baseUrl}/push/test/${userId}`, {
                    method: 'POST'
                });
                const data = await response.json();

                let message = '<strong>推送测试结果:</strong><br>';
                message += `测试结果: ${data.success ? '✅ 成功' : '❌ 失败'}<br>`;
                message += `设备数量: ${data.deviceCount}<br>`;
                message += `服务状态: ${data.serviceAvailable ? '✅ 可用' : '❌ 不可用'}<br>`;
                message += `消息: ${data.message}`;

                const statusType = data.success ? 'success' : 'error';
                showStatus('pushStatus', message, statusType);
                log(`推送测试完成: ${data.message}`);
            } catch (error) {
                log(`推送测试失败: ${error.message}`);
                showStatus('pushStatus', `测试失败: ${error.message}`, 'error');
            }
        }

        async function checkUserPushAvailability() {
            const userId = document.getElementById('testUserId').value || 'android_user_default';
            log(`检查用户推送可用性: ${userId}`);
            
            try {
                const response = await apiCall(`${baseUrl}/admin/push/check/${userId}`);
                const data = await response.json();

                let message = '<strong>用户推送可用性:</strong><br>';
                message += `推送可用: ${data.data.canReceivePush ? '✅ 是' : '❌ 否'}<br>`;
                message += `设备数量: ${data.data.deviceCount}<br>`;
                message += `服务可用: ${data.data.serviceAvailable ? '✅ 是' : '❌ 否'}<br>`;
                message += `用户ID: ${data.data.userId}<br>`;
                message += `说明: ${data.data.message}`;

                const statusType = data.data.canReceivePush ? 'success' : 'error';
                showStatus('pushStatus', message, statusType);
                log('用户推送可用性检查完成');
            } catch (error) {
                log(`推送可用性检查失败: ${error.message}`);
                showStatus('pushStatus', `检查失败: ${error.message}`, 'error');
            }
        }

        async function checkDeviceInfo() {
            const userId = document.getElementById('deviceUserId').value || 'android_user_default';
            log(`检查设备信息: ${userId}`);
            
            try {
                const response = await apiCall(`${baseUrl}/device/info/${userId}`);
                const data = await response.json();

                let message = '<strong>设备信息:</strong><br>';
                message += `总设备数: ${data.data?.totalDevices || 0}<br>`;
                message += `活跃设备: ${data.data?.activeDevices || 0}<br>`;

                if (data.data?.devices && data.data.devices.length > 0) {
                    message += '<br><strong>设备列表:</strong><br>';
                    data.data.devices.forEach((device, index) => {
                        message += `${index + 1}. 类型: ${device.deviceType}, 状态: ${device.status}, 注册时间: ${device.registeredAt}<br>`;
                    });
                }

                showStatus('deviceInfo', message, 'info');
                log('设备信息检查完成');
            } catch (error) {
                log(`设备信息检查失败: ${error.message}`);
                showStatus('deviceInfo', `检查失败: ${error.message}`, 'error');
            }
        }

        function openSwaggerDocs() {
            window.open('/wedin/swagger-ui/index.html', '_blank');
        }
    </script>
</body>
</html>
