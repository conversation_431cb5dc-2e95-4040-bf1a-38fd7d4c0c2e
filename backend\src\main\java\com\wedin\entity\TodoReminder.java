package com.wedin.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 待办事项提醒实体
 */
@Entity
@Table(name = "todo_reminders")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TodoReminder {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的消息ID
     */
    @Column(name = "message_id", nullable = false)
    private Long messageId;

    /**
     * 用户OpenID
     */
    @Column(name = "user_open_id", nullable = false, length = 100)
    private String userOpenId;

    /**
     * 待办事项内容
     */
    @Column(name = "todo_content", nullable = false, columnDefinition = "TEXT")
    private String todoContent;

    /**
     * 原始触发时间（用户指定的时间）
     */
    @Column(name = "trigger_time", nullable = false)
    private LocalDateTime triggerTime;

    /**
     * 提醒时间（实际发送提醒的时间，通常比触发时间提前）
     */
    @Column(name = "reminder_time", nullable = false)
    private LocalDateTime reminderTime;

    /**
     * 提醒状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ReminderStatus status;

    /**
     * 提醒次数
     */
    @Column(name = "reminder_count", nullable = false)
    @Builder.Default
    private Integer reminderCount = 0;

    /**
     * 最后提醒时间
     */
    @Column(name = "last_reminder_time")
    private LocalDateTime lastReminderTime;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 提醒状态枚举
     */
    public enum ReminderStatus {
        /**
         * 等待提醒
         */
        PENDING("等待提醒"),
        
        /**
         * 已提醒
         */
        REMINDED("已提醒"),
        
        /**
         * 已完成
         */
        COMPLETED("已完成"),
        
        /**
         * 已取消
         */
        CANCELLED("已取消"),
        
        /**
         * 已过期
         */
        EXPIRED("已过期");

        private final String displayName;

        ReminderStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            status = ReminderStatus.PENDING;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
