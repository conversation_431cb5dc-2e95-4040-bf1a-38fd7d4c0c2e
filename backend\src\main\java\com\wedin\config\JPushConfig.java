package com.wedin.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 极光推送配置类
 */
@Configuration
@ConfigurationProperties(prefix = "jpush")
@Data
@Slf4j
public class JPushConfig {

    /**
     * 是否启用极光推送功能
     */
    private boolean enabled = true;

    /**
     * 极光推送应用Key
     */
    private String appKey;

    /**
     * 极光推送主密钥
     */
    private String masterSecret;

    /**
     * 是否为生产环境
     */
    private boolean production = false;

    /**
     * 推送消息TTL (秒)
     */
    private long timeToLive = 86400;

    @PostConstruct
    public void init() {
        if (enabled) {
            if (appKey == null || appKey.isEmpty() || 
                masterSecret == null || masterSecret.isEmpty()) {
                log.warn("极光推送配置不完整，请检查配置文件中的 jpush.app-key, jpush.master-secret");
                enabled = false;
            } else {
                log.info("极光推送配置初始化成功: appKey={}, production={}, enabled={}", 
                        appKey, production, enabled);
            }
        } else {
            log.info("极光推送功能已禁用");
        }
    }
}
