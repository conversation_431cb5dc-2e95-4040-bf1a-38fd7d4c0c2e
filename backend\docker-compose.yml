services:
  wedin-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wedin-ai-assistant
    restart: unless-stopped
    ports:
      - "8080:8080"
    # 在 Linux 系统上添加 host.docker.internal 映射
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      # 时区配置
      - TZ=Asia/Shanghai

      # 数据库配置 - 连接到宿主机的MySQL
      - DB_HOST=${DB_HOST:-host.docker.internal}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      
      # 微信公众号配置
      - WECHAT_APP_ID=${WECHAT_APP_ID}
      - WECHAT_APP_SECRET=${WECHAT_APP_SECRET}
      - WECHAT_TOKEN=${WECHAT_TOKEN}
      - WECHAT_ENCODING_AES_KEY=${WECHAT_ENCODING_AES_KEY}
      
      # DeepSeek API配置
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      
      # 极光推送配置
      - JPUSH_APP_KEY=${JPUSH_APP_KEY:-31fcf148299a5afd6980c623}
      - JPUSH_MASTER_SECRET=${JPUSH_MASTER_SECRET:-7f6876d0f06681bbe9fc3139}
      
      # 百度语音识别配置
      - BAIDU_SPEECH_API_KEY=${BAIDU_SPEECH_API_KEY}
      - BAIDU_SPEECH_SECRET_KEY=${BAIDU_SPEECH_SECRET_KEY}
      
      # JWT配置
      - JWT_SECRET=${JWT_SECRET:-wedin-ai-assistant-jwt-secret-key-2024-very-long-and-secure}
      - JWT_ACCESS_TOKEN_EXPIRATION=${JWT_ACCESS_TOKEN_EXPIRATION:-86400000}
      - JWT_REFRESH_TOKEN_EXPIRATION=${JWT_REFRESH_TOKEN_EXPIRATION:-604800000}
      - JWT_ISSUER=${JWT_ISSUER:-wedin-ai-assistant}
      
      # Spring Profile
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-prod}
      
      # JVM配置
      - JAVA_OPTS=-Xmx1g -Xms512m -Djava.security.egd=file:/dev/./urandom
    
    volumes:
      # 日志目录挂载
      - ./logs:/app/logs
      # 如果有文件上传功能，可以挂载上传目录
      - ./uploads:/app/uploads
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
