package com.wedin.controller;

import com.wedin.dto.ApiResponse;
import com.wedin.service.DeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备管理控制器
 */
@RestController
@RequestMapping("/api/device")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(originPatterns = "*")
@PreAuthorize("isAuthenticated()")
public class DeviceController {

    private final DeviceService deviceService;

    /**
     * 注册设备推送token
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<Map<String, String>>> registerDevice(@RequestBody Map<String, String> request) {
        try {
            String token = request.get("token");
            String deviceType = request.get("deviceType");
            String userId = request.get("userId");

            log.info("收到设备注册请求: userId={}, deviceType={}, token={}", userId, deviceType, token);

            if (token == null || deviceType == null || userId == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(400, "缺少必要参数: token, deviceType, userId", null));
            }

            boolean success = deviceService.registerDevice(userId, token, deviceType);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", success ? "success" : "failed");
            response.put("message", success ? "设备注册成功" : "设备注册失败");

            return ResponseEntity.ok(new ApiResponse<>(200, "设备注册处理完成", response));
        } catch (Exception e) {
            log.error("设备注册失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(500, "设备注册失败: " + e.getMessage(), null));
        }
    }

    /**
     * 更新设备设置
     */
    @PutMapping("/settings")
    public ResponseEntity<ApiResponse<Void>> updateDeviceSettings(@RequestBody Map<String, Object> request) {
        try {
            String userId = (String) request.get("userId");
            String deviceType = (String) request.get("deviceType");
            
            log.info("收到设备设置更新请求: userId={}, deviceType={}", userId, deviceType);

            if (userId == null || deviceType == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(400, "缺少必要参数: userId, deviceType", null));
            }

            boolean success = deviceService.updateDeviceSettings(userId, deviceType, request);
            
            if (success) {
                return ResponseEntity.ok(new ApiResponse<>(200, "设备设置更新成功", null));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(400, "设备设置更新失败", null));
            }
        } catch (Exception e) {
            log.error("设备设置更新失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(500, "设备设置更新失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取设备信息
     */
    @GetMapping("/info/{userId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDeviceInfo(@PathVariable String userId) {
        try {
            log.info("获取设备信息: userId={}", userId);
            
            Map<String, Object> deviceInfo = deviceService.getDeviceInfo(userId);
            
            return ResponseEntity.ok(new ApiResponse<>(200, "获取设备信息成功", deviceInfo));
        } catch (Exception e) {
            log.error("获取设备信息失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(500, "获取设备信息失败: " + e.getMessage(), null));
        }
    }

    /**
     * 注销设备
     */
    @DeleteMapping("/unregister")
    public ResponseEntity<ApiResponse<Void>> unregisterDevice(@RequestBody Map<String, String> request) {
        try {
            String userId = request.get("userId");
            String deviceType = request.get("deviceType");
            String token = request.get("token");

            log.info("收到设备注销请求: userId={}, deviceType={}, token={}", userId, deviceType, token);

            if (userId == null || deviceType == null) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(400, "缺少必要参数: userId, deviceType", null));
            }

            boolean success = deviceService.unregisterDevice(userId, deviceType, token);
            
            if (success) {
                return ResponseEntity.ok(new ApiResponse<>(200, "设备注销成功", null));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(400, "设备注销失败", null));
            }
        } catch (Exception e) {
            log.error("设备注销失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(500, "设备注销失败: " + e.getMessage(), null));
        }
    }
}
