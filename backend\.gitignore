# Maven构建输出
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml

# Spring Boot特定
*.pid
*.jar.original

# 配置文件（包含敏感信息）
application-local.properties
application-dev.properties
application-prod.properties
application-*.yml
!application.yml

# 数据库文件
*.db
*.sqlite
*.sqlite3
*.h2.db
*.trace.db
*.lock.db

# 日志文件
logs/
*.log
*.log.*

# IDE相关
.idea/
*.iml
*.iws
*.ipr

# 极光推送服务器端配置
jpush-server-config.properties

# 环境变量
.env
.env.local

# 临时文件
*.tmp
*.temp

# 测试覆盖率
jacoco.exec
target/site/jacoco/

# Spring Boot DevTools
.spring-boot-devtools.properties 