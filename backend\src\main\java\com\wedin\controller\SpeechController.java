package com.wedin.controller;

import com.wedin.service.BaiduSpeechService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/speech")
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"}, allowCredentials = "true")
@PreAuthorize("isAuthenticated()")
public class SpeechController {

    @Autowired
    private BaiduSpeechService baiduSpeechService;

    /**
     * 语音识别接口
     */
    @PostMapping("/recognize")
    public ResponseEntity<Map<String, Object>> recognizeSpeech(
            @RequestParam("audio") MultipartFile audioFile,
            @RequestParam(value = "format", defaultValue = "webm") String format,
            @RequestParam(value = "rate", defaultValue = "16000") Integer rate,
            @RequestParam(value = "channel", defaultValue = "1") Integer channel,
            @RequestParam(value = "cuid", defaultValue = "web-client") String cuid
    ) {
        Map<String, Object> response = new HashMap<>();

        try {
            System.out.println("收到语音识别请求: format=" + format + ", rate=" + rate +
                             ", channel=" + channel + ", cuid=" + cuid);

            // 检查文件
            if (audioFile.isEmpty()) {
                System.out.println("音频文件为空");
                response.put("err_no", 3001);
                response.put("err_msg", "音频文件为空");
                return ResponseEntity.badRequest().body(response);
            }

            System.out.println("音频文件信息: 原始名称=" + audioFile.getOriginalFilename() +
                             ", 大小=" + audioFile.getSize() + " bytes");
            
            // 检查文件大小 (百度API限制：小于60秒，约10MB)
            if (audioFile.getSize() > 10 * 1024 * 1024) {
                System.out.println("音频文件过大: " + audioFile.getSize() + " bytes");
                response.put("err_no", 3002);
                response.put("err_msg", "音频文件过大，请控制在60秒内");
                return ResponseEntity.badRequest().body(response);
            }

            // 调用百度语音识别服务
            System.out.println("开始调用百度语音识别服务...");
            Map<String, Object> result = baiduSpeechService.recognizeAudio(
                audioFile.getBytes(),
                format,
                rate,
                channel,
                cuid
            );

            System.out.println("语音识别结果: " + result);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            response.put("err_no", 5000);
            response.put("err_msg", "语音识别服务异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取访问令牌（如果需要前端直接调用百度API）
     */
    @GetMapping("/token")
    public ResponseEntity<Map<String, Object>> getAccessToken() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String token = baiduSpeechService.getAccessToken();
            response.put("access_token", token);
            response.put("expires_in", 2592000); // 30天
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("error", "获取访问令牌失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}