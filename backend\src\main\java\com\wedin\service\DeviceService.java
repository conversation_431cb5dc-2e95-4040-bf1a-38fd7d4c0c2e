package com.wedin.service;

import com.wedin.entity.DeviceToken;
import com.wedin.repository.DeviceTokenRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 设备管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceService {

    private final DeviceTokenRepository deviceTokenRepository;

    /**
     * 注册设备推送token
     */
    @Transactional
    public boolean registerDevice(String userId, String token, String deviceType) {
        try {
            log.info("注册设备: userId={}, deviceType={}, token={}", userId, deviceType, token);

            // 检查是否已存在相同的token
            Optional<DeviceToken> existingToken = deviceTokenRepository
                    .findByUserIdAndTokenAndDeviceType(userId, token, deviceType);

            if (existingToken.isPresent()) {
                DeviceToken deviceToken = existingToken.get();
                // 更新现有token的状态和时间
                deviceToken.setStatus(DeviceToken.TokenStatus.ACTIVE);
                deviceToken.setUpdatedAt(LocalDateTime.now());
                deviceToken.setLastActiveAt(LocalDateTime.now());
                
                deviceTokenRepository.save(deviceToken);
                log.info("更新现有设备token: tokenId={}", deviceToken.getId());
                return true;
            }

            // 创建新的设备token记录
            DeviceToken deviceToken = DeviceToken.builder()
                    .userId(userId)
                    .token(token)
                    .deviceType(deviceType)
                    .status(DeviceToken.TokenStatus.ACTIVE)
                    .registeredAt(LocalDateTime.now())
                    .lastActiveAt(LocalDateTime.now())
                    .build();

            deviceToken = deviceTokenRepository.save(deviceToken);
            log.info("创建新设备token成功: tokenId={}", deviceToken.getId());
            
            return true;
        } catch (Exception e) {
            log.error("注册设备失败: userId={}, deviceType={}, error={}", userId, deviceType, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新设备设置
     */
    @Transactional
    public boolean updateDeviceSettings(String userId, String deviceType, Map<String, Object> settings) {
        try {
            log.info("更新设备设置: userId={}, deviceType={}, settings={}", userId, deviceType, settings);

            List<DeviceToken> tokens = deviceTokenRepository
                    .findByUserIdAndDeviceTypeAndStatus(userId, deviceType, DeviceToken.TokenStatus.ACTIVE);

            if (tokens.isEmpty()) {
                log.warn("未找到活跃的设备token: userId={}, deviceType={}", userId, deviceType);
                return false;
            }

            // 更新所有匹配的设备token
            for (DeviceToken token : tokens) {
                token.setUpdatedAt(LocalDateTime.now());
                token.setLastActiveAt(LocalDateTime.now());
                
                // 这里可以根据需要添加更多设置字段的更新逻辑
                // 例如：推送开关、推送时间段等
                
                deviceTokenRepository.save(token);
            }

            log.info("设备设置更新成功: userId={}, deviceType={}, 更新了{}个设备", 
                    userId, deviceType, tokens.size());
            return true;
        } catch (Exception e) {
            log.error("更新设备设置失败: userId={}, deviceType={}, error={}", 
                    userId, deviceType, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取设备信息
     */
    public Map<String, Object> getDeviceInfo(String userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<DeviceToken> tokens = deviceTokenRepository.findByUserIdOrderByRegisteredAtDesc(userId);
            
            result.put("totalDevices", tokens.size());
            result.put("activeDevices", tokens.stream()
                    .filter(token -> token.getStatus() == DeviceToken.TokenStatus.ACTIVE)
                    .count());
            result.put("devices", tokens);
            
            log.info("获取设备信息成功: userId={}, 总设备数={}", userId, tokens.size());
        } catch (Exception e) {
            log.error("获取设备信息失败: userId={}, error={}", userId, e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 注销设备
     */
    @Transactional
    public boolean unregisterDevice(String userId, String deviceType, String token) {
        try {
            log.info("注销设备: userId={}, deviceType={}, token={}", userId, deviceType, token);

            if (token != null) {
                // 根据具体token注销
                Optional<DeviceToken> deviceToken = deviceTokenRepository
                        .findByUserIdAndTokenAndDeviceType(userId, token, deviceType);
                
                if (deviceToken.isPresent()) {
                    DeviceToken dt = deviceToken.get();
                    dt.setStatus(DeviceToken.TokenStatus.INACTIVE);
                    dt.setUpdatedAt(LocalDateTime.now());
                    deviceTokenRepository.save(dt);
                    
                    log.info("注销指定设备token成功: tokenId={}", dt.getId());
                    return true;
                } else {
                    log.warn("未找到要注销的设备token: userId={}, deviceType={}, token={}", 
                            userId, deviceType, token);
                    return false;
                }
            } else {
                // 注销该用户该类型的所有设备
                List<DeviceToken> tokens = deviceTokenRepository
                        .findByUserIdAndDeviceTypeAndStatus(userId, deviceType, DeviceToken.TokenStatus.ACTIVE);
                
                for (DeviceToken dt : tokens) {
                    dt.setStatus(DeviceToken.TokenStatus.INACTIVE);
                    dt.setUpdatedAt(LocalDateTime.now());
                    deviceTokenRepository.save(dt);
                }
                
                log.info("注销用户所有设备成功: userId={}, deviceType={}, 注销了{}个设备", 
                        userId, deviceType, tokens.size());
                return true;
            }
        } catch (Exception e) {
            log.error("注销设备失败: userId={}, deviceType={}, error={}", 
                    userId, deviceType, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取用户的活跃设备token列表
     */
    public List<DeviceToken> getActiveDeviceTokens(String userId, String deviceType) {
        return deviceTokenRepository.findByUserIdAndDeviceTypeAndStatus(
                userId, deviceType, DeviceToken.TokenStatus.ACTIVE);
    }

    /**
     * 获取用户的所有活跃设备token
     */
    public List<DeviceToken> getAllActiveDeviceTokens(String userId) {
        return deviceTokenRepository.findByUserIdAndStatus(userId, DeviceToken.TokenStatus.ACTIVE);
    }

    /**
     * 清理过期的设备token
     */
    @Transactional
    public void cleanupExpiredTokens() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            
            // 将长时间未活跃的token标记为过期
            List<DeviceToken> inactiveTokens = deviceTokenRepository
                    .findInactiveTokens(cutoffTime);
            
            for (DeviceToken token : inactiveTokens) {
                token.setStatus(DeviceToken.TokenStatus.EXPIRED);
                token.setUpdatedAt(LocalDateTime.now());
                deviceTokenRepository.save(token);
            }
            
            log.info("清理过期设备token完成，处理了{}个token", inactiveTokens.size());
        } catch (Exception e) {
            log.error("清理过期设备token失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取指定类型的所有设备Token
     */
    public List<DeviceToken> getAllDeviceTokensByType(String deviceType) {
        try {
            return deviceTokenRepository.findByDeviceTypeAndStatus(deviceType, DeviceToken.TokenStatus.ACTIVE);
        } catch (Exception e) {
            log.error("获取设备Token列表失败: deviceType={}, error={}", deviceType, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 获取所有活跃的设备Token
     */
    public List<DeviceToken> getAllActiveDeviceTokens() {
        try {
            return deviceTokenRepository.findAll().stream()
                    .filter(token -> token.getStatus() == DeviceToken.TokenStatus.ACTIVE)
                    .toList();
        } catch (Exception e) {
            log.error("获取所有活跃设备Token失败: {}", e.getMessage(), e);
            return List.of();
        }
    }
}
