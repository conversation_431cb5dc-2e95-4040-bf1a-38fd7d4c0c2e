package com.wedin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 待办事项提醒配置
 */
@Configuration
@ConfigurationProperties(prefix = "todo-reminder")
@Data
public class TodoReminderConfig {

    /**
     * 是否启用待办事项提醒功能
     */
    private boolean enabled = true;

    /**
     * 提前多少分钟提醒
     */
    private int advanceMinutes = 10;

    /**
     * 扫描间隔（毫秒）
     */
    private long scanInterval = 60000;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 清理多少天前的已完成提醒
     */
    private int cleanupDays = 30;
}
