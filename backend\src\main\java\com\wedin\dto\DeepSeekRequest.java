package com.wedin.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * DeepSeek API请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeepSeekRequest {

    /**
     * 模型名称
     */
    private String model;

    /**
     * 对话消息列表
     */
    private List<ChatMessage> messages;

    /**
     * 最大token数
     */
    private Integer maxTokens;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 是否流式输出
     */
    private Boolean stream;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChatMessage {
        /**
         * 角色: system, user, assistant
         */
        private String role;

        /**
         * 消息内容
         */
        private String content;
    }
}
