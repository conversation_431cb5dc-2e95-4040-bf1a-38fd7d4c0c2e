package com.wedin.controller;

import com.wedin.dto.ApiResponse;
import com.wedin.service.JPushService;
import com.wedin.service.DeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理控制器 - 提供系统管理和调试功能
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("isAuthenticated()")
@Tag(name = "系统管理", description = "系统管理和调试相关接口")
@SecurityRequirement(name = "bearerAuth")
public class AdminController {

    private final JPushService jPushService;
    private final DeviceService deviceService;

    /**
     * 获取推送服务状态
     */
    @Operation(summary = "获取推送服务状态", description = "检查推送服务的整体状态")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "状态获取成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "未认证")
    })
    @GetMapping("/push/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPushStatus() {
        log.info("获取推送服务状态");
        
        Map<String, Object> status = new HashMap<>();
        try {
            boolean serviceAvailable = jPushService.isServiceAvailable();
            status.put("serviceAvailable", serviceAvailable);
            status.put("serviceName", "JPush");
            status.put("checkTime", LocalDateTime.now());
            status.put("message", serviceAvailable ? "推送服务正常" : "推送服务不可用");
            
            return ResponseEntity.ok(new ApiResponse<>(200, "推送状态获取成功", status));
        } catch (Exception e) {
            log.error("获取推送状态失败", e);
            status.put("serviceAvailable", false);
            status.put("error", e.getMessage());
            return ResponseEntity.ok(new ApiResponse<>(200, "推送状态获取完成", status));
        }
    }

    /**
     * 检查用户推送可用性
     */
    @Operation(summary = "检查用户推送可用性", description = "检查指定用户是否可以接收推送")
    @GetMapping("/push/check/{userId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkPushAvailability(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        log.info("检查用户推送可用性: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        try {
            long deviceCountLong = jPushService.getUserDeviceCount(userId);
            int deviceCount = (int) deviceCountLong;
            boolean serviceAvailable = jPushService.isServiceAvailable();
            boolean canReceivePush = deviceCount > 0 && serviceAvailable;

            result.put("canReceivePush", canReceivePush);
            result.put("deviceCount", deviceCount);
            result.put("serviceAvailable", serviceAvailable);
            result.put("userId", userId);
            result.put("message", canReceivePush ? "用户可以接收推送" : "用户无法接收推送");
            
            return ResponseEntity.ok(new ApiResponse<>(200, "推送可用性检查完成", result));
        } catch (Exception e) {
            log.error("检查推送可用性失败", e);
            result.put("canReceivePush", false);
            result.put("error", e.getMessage());
            return ResponseEntity.ok(new ApiResponse<>(200, "推送可用性检查完成", result));
        }
    }

    /**
     * 获取系统健康状态
     */
    @Operation(summary = "获取系统健康状态", description = "获取系统各组件的健康状态")
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemHealth() {
        log.info("获取系统健康状态");
        
        Map<String, Object> health = new HashMap<>();
        try {
            // 检查推送服务
            boolean pushServiceHealthy = jPushService.isServiceAvailable();
            
            // 检查数据库（通过设备服务）
            boolean databaseHealthy = true;
            try {
                deviceService.getDeviceInfo("health_check");
            } catch (Exception e) {
                databaseHealthy = false;
            }
            
            health.put("pushService", pushServiceHealthy);
            health.put("database", databaseHealthy);
            health.put("overall", pushServiceHealthy && databaseHealthy);
            health.put("checkTime", LocalDateTime.now());
            health.put("message", "系统健康检查完成");
            
            return ResponseEntity.ok(new ApiResponse<>(200, "健康检查完成", health));
        } catch (Exception e) {
            log.error("系统健康检查失败", e);
            health.put("overall", false);
            health.put("error", e.getMessage());
            return ResponseEntity.ok(new ApiResponse<>(200, "健康检查完成", health));
        }
    }

    /**
     * 获取系统统计信息
     */
    @Operation(summary = "获取系统统计信息", description = "获取系统的统计数据")
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStats() {
        log.info("获取系统统计信息");
        
        Map<String, Object> stats = new HashMap<>();
        try {
            // 这里可以添加更多统计信息
            stats.put("timestamp", LocalDateTime.now());
            stats.put("message", "统计信息获取成功");
            
            return ResponseEntity.ok(new ApiResponse<>(200, "统计信息获取成功", stats));
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            stats.put("error", e.getMessage());
            return ResponseEntity.ok(new ApiResponse<>(200, "统计信息获取完成", stats));
        }
    }
}
