package com.wedin.service;

import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.Notification;
import com.wedin.config.JPushConfig;
import com.wedin.entity.DeviceToken;
import com.wedin.entity.TodoReminder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 极光推送服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JPushService {

    private final DeviceService deviceService;
    private final JPushConfig jpushConfig;
    
    private JPushClient jpushClient;
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("MM-dd HH:mm");

    @PostConstruct
    public void init() {
        if (jpushConfig.isEnabled() && 
            jpushConfig.getAppKey() != null && !jpushConfig.getAppKey().isEmpty()) {
            
            try {
                // 创建JPush客户端
                ClientConfig clientConfig = ClientConfig.getInstance();
                jpushClient = new JPushClient(jpushConfig.getMasterSecret(), 
                                            jpushConfig.getAppKey(), 
                                            null, 
                                            clientConfig);
                
                log.info("极光推送服务初始化成功: appKey={}, production={}, enabled={}", 
                        jpushConfig.getAppKey(), jpushConfig.isProduction(), jpushConfig.isEnabled());
            } catch (Exception e) {
                log.error("极光推送服务初始化失败", e);
                jpushClient = null;
            }
        } else {
            log.warn("极光推送配置不完整或已禁用，跳过初始化");
        }
    }

    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        return jpushConfig.isEnabled() && jpushClient != null;
    }

    /**
     * 发送待办事项提醒通知
     */
    public boolean sendReminderNotification(TodoReminder reminder) {
        if (!isServiceAvailable()) {
            log.warn("极光推送服务不可用，跳过推送");
            return false;
        }

        try {
            log.info("发送极光推送提醒通知: reminderId={}, content={}", 
                    reminder.getId(), reminder.getTodoContent());

            List<DeviceToken> devices = deviceService.getActiveDeviceTokens(reminder.getUserOpenId(), "JPUSH");

            if (devices.isEmpty()) {
                log.warn("用户没有活跃的极光推送设备: userId={}", reminder.getUserOpenId());
                return false;
            }

            String title = "📝 待办事项提醒";
            String content = String.format("提醒时间：%s\n内容：%s", 
                    reminder.getTriggerTime().format(TIME_FORMATTER), 
                    reminder.getTodoContent());

            boolean anySuccess = false;

            for (DeviceToken device : devices) {
                try {
                    boolean sent = sendToDevice(device.getToken(), title, content, reminder);
                    if (sent) {
                        anySuccess = true;
                    }
                } catch (Exception e) {
                    log.warn("极光推送到设备失败: deviceId={}, error={}", 
                            device.getId(), e.getMessage());
                }
            }

            return anySuccess;

        } catch (Exception e) {
            log.error("发送极光推送提醒通知失败: reminderId={}, error={}", 
                    reminder.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送测试推送通知
     */
    public boolean sendTestNotification(String userId) {
        if (!isServiceAvailable()) {
            log.warn("极光推送服务不可用，跳过推送");
            return false;
        }

        try {
            log.info("发送极光推送测试推送: userId={}", userId);

            List<DeviceToken> devices = deviceService.getActiveDeviceTokens(userId, "JPUSH");

            if (devices.isEmpty()) {
                log.warn("用户没有活跃的极光推送设备: userId={}", userId);
                return false;
            }

            String title = "🔔 WedinAI 测试通知";
            String content = "这是一条测试推送通知，如果您看到这条消息，说明极光推送功能正常工作！";

            boolean anySuccess = false;

            for (DeviceToken device : devices) {
                try {
                    // 创建一个临时的提醒对象用于测试
                    TodoReminder testReminder = TodoReminder.builder()
                            .id(-1L)
                            .todoContent("测试推送")
                            .build();

                    boolean sent = sendToDevice(device.getToken(), title, content, testReminder);
                    if (sent) {
                        anySuccess = true;
                    }
                } catch (Exception e) {
                    log.warn("极光推送测试推送到设备失败: deviceId={}, error={}", 
                            device.getId(), e.getMessage());
                }
            }

            return anySuccess;

        } catch (Exception e) {
            log.error("发送极光推送测试通知失败: userId={}, error={}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送推送到指定设备
     */
    private boolean sendToDevice(String registrationId, String title, String content, TodoReminder reminder) {
        try {
            log.info("发送极光推送消息到设备: registrationId={}, title={}, content={}", 
                    registrationId.substring(0, Math.min(10, registrationId.length())) + "...", title, content);

            // 构建推送载荷
            PushPayload payload = PushPayload.newBuilder()
                    .setPlatform(Platform.android())
                    .setAudience(Audience.registrationId(registrationId))
                    .setNotification(Notification.newBuilder()
                            .setAlert(content)
                            .addPlatformNotification(AndroidNotification.newBuilder()
                                    .setTitle(title)
                                    .setAlert(content)
                                    .addExtra("reminder_id", reminder.getId() != null ? reminder.getId().toString() : "")
                                    .addExtra("todo_content", reminder.getTodoContent())
                                    .build())
                            .build())
                    .setMessage(Message.newBuilder()
                            .setMsgContent(content)
                            .addExtra("reminder_id", reminder.getId() != null ? reminder.getId().toString() : "")
                            .addExtra("todo_content", reminder.getTodoContent())
                            .build())
                    .setOptions(cn.jpush.api.push.model.Options.newBuilder()
                            .setTimeToLive(jpushConfig.getTimeToLive())
                            .setApnsProduction(jpushConfig.isProduction())
                            .build())
                    .build();

            // 发送推送
            PushResult result = jpushClient.sendPush(payload);
            
            if (result.isResultOK()) {
                log.info("极光推送消息发送成功: registrationId={}, msgId={}", 
                        registrationId.substring(0, Math.min(10, registrationId.length())) + "...", 
                        result.msg_id);
                return true;
            } else {
                log.warn("极光推送消息发送失败: registrationId={}, error={}", 
                        registrationId.substring(0, Math.min(10, registrationId.length())) + "...", 
                        result.getOriginalContent());
                return false;
            }

        } catch (APIConnectionException e) {
            log.error("极光推送连接异常: registrationId={}, error={}", 
                    registrationId.substring(0, Math.min(10, registrationId.length())) + "...", e.getMessage());
            return false;
        } catch (APIRequestException e) {
            log.error("极光推送请求异常: registrationId={}, error={}", 
                    registrationId.substring(0, Math.min(10, registrationId.length())) + "...", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("发送极光推送消息失败: registrationId={}, error={}", 
                    registrationId.substring(0, Math.min(10, registrationId.length())) + "...", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取用户设备数量
     */
    public long getUserDeviceCount(String userId) {
        try {
            List<DeviceToken> devices = deviceService.getActiveDeviceTokens(userId, "JPUSH");
            return devices.size();
        } catch (Exception e) {
            log.error("获取用户极光推送设备数量失败: userId={}, error={}", userId, e.getMessage());
            return 0;
        }
    }

    /**
     * 批量推送
     */
    public boolean sendBatchNotification(List<String> registrationIds, String title, String content) {
        if (!isServiceAvailable()) {
            log.warn("极光推送服务不可用，跳过推送");
            return false;
        }

        try {
            log.info("发送极光推送批量通知: registrationIds数量={}, title={}", registrationIds.size(), title);

            // 构建推送载荷
            PushPayload payload = PushPayload.newBuilder()
                    .setPlatform(Platform.android())
                    .setAudience(Audience.registrationId(registrationIds))
                    .setNotification(Notification.newBuilder()
                            .setAlert(content)
                            .addPlatformNotification(AndroidNotification.newBuilder()
                                    .setTitle(title)
                                    .setAlert(content)
                                    .build())
                            .build())
                    .setOptions(cn.jpush.api.push.model.Options.newBuilder()
                            .setTimeToLive(jpushConfig.getTimeToLive())
                            .setApnsProduction(jpushConfig.isProduction())
                            .build())
                    .build();

            // 发送推送
            PushResult result = jpushClient.sendPush(payload);
            
            if (result.isResultOK()) {
                log.info("极光推送批量通知发送成功: registrationIds数量={}, msgId={}", 
                        registrationIds.size(), result.msg_id);
                return true;
            } else {
                log.warn("极光推送批量通知发送失败: registrationIds数量={}, error={}", 
                        registrationIds.size(), result.getOriginalContent());
                return false;
            }

        } catch (Exception e) {
            log.error("发送极光推送批量通知失败: registrationIds数量={}, error={}", 
                    registrationIds.size(), e.getMessage(), e);
            return false;
        }
    }
}
