package com.wedin.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单调试控制器 - 不依赖其他类，直接查询数据库
 */
@RestController
@RequestMapping("/api/simple-debug")
@Slf4j
@CrossOrigin(originPatterns = "*")
public class SimpleDebugController {

    @Autowired
    private DataSource dataSource;

    /**
     * 获取待办提醒状态
     */
    @GetMapping("/reminder-status")
    public ResponseEntity<Map<String, Object>> getReminderStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalDateTime now = LocalDateTime.now();
            result.put("currentTime", now.toString());
            
            try (Connection conn = dataSource.getConnection()) {
                // 查询总提醒数
                String totalSql = "SELECT COUNT(*) as total FROM todo_reminders";
                try (PreparedStatement ps = conn.prepareStatement(totalSql);
                     ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        result.put("totalReminders", rs.getInt("total"));
                    }
                }
                
                // 查询待处理提醒数
                String pendingSql = "SELECT COUNT(*) as pending FROM todo_reminders WHERE status = 'PENDING'";
                try (PreparedStatement ps = conn.prepareStatement(pendingSql);
                     ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        result.put("pendingReminders", rs.getInt("pending"));
                    }
                }
                
                // 查询应该被处理的提醒数（提醒时间已到）
                String dueSql = "SELECT COUNT(*) as due FROM todo_reminders WHERE status = 'PENDING' AND reminder_time <= ?";
                try (PreparedStatement ps = conn.prepareStatement(dueSql)) {
                    ps.setObject(1, now);
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            result.put("dueReminders", rs.getInt("due"));
                        }
                    }
                }
                
                // 查询待处理提醒详情
                String detailSql = "SELECT id, todo_content, reminder_time, trigger_time, user_open_id FROM todo_reminders WHERE status = 'PENDING' AND reminder_time <= ? ORDER BY reminder_time LIMIT 10";
                try (PreparedStatement ps = conn.prepareStatement(detailSql)) {
                    ps.setObject(1, now);
                    try (ResultSet rs = ps.executeQuery()) {
                        StringBuilder details = new StringBuilder();
                        int count = 0;
                        while (rs.next()) {
                            count++;
                            details.append(String.format("ID:%d, 内容:%s, 提醒时间:%s, 触发时间:%s, 用户:%s\n",
                                rs.getLong("id"),
                                rs.getString("todo_content"),
                                rs.getObject("reminder_time"),
                                rs.getObject("trigger_time"),
                                rs.getString("user_open_id")
                            ));
                        }
                        result.put("dueReminderDetails", details.toString());
                        result.put("dueReminderCount", count);
                    }
                }
            }
            
            log.info("获取提醒状态成功: {}", result);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取提醒状态失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 检查定时任务配置
     */
    @GetMapping("/scheduler-status")
    public ResponseEntity<Map<String, Object>> getSchedulerStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查Spring Boot应用是否启用了定时任务
            result.put("currentTime", LocalDateTime.now().toString());
            result.put("message", "如果能看到这个响应，说明应用正在运行");
            
            // 简单的健康检查
            result.put("healthy", true);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取调度器状态失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 创建测试提醒
     */
    @PostMapping("/create-test-reminder")
    public ResponseEntity<Map<String, Object>> createTestReminder(
            @RequestParam(defaultValue = "test_user") String userId,
            @RequestParam(defaultValue = "1") int minutesFromNow) {

        Map<String, Object> result = new HashMap<>();

        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime triggerTime = now.plusMinutes(minutesFromNow);
            LocalDateTime reminderTime = triggerTime.minusMinutes(3); // 提前3分钟提醒

            // 如果提醒时间已经过了，设置为下一个整分钟
            if (reminderTime.isBefore(now)) {
                reminderTime = now.withSecond(0).withNano(0).plusMinutes(1);
            }
            
            try (Connection conn = dataSource.getConnection()) {
                String sql = "INSERT INTO todo_reminders (message_id, user_open_id, todo_content, trigger_time, reminder_time, status, reminder_count, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                try (PreparedStatement ps = conn.prepareStatement(sql)) {
                    ps.setLong(1, -1L); // 测试用的消息ID
                    ps.setString(2, userId);
                    ps.setString(3, "测试提醒 - " + now.toString());
                    ps.setObject(4, triggerTime);
                    ps.setObject(5, reminderTime);
                    ps.setString(6, "PENDING");
                    ps.setInt(7, 0); // reminder_count 默认为 0
                    ps.setObject(8, now);
                    ps.setObject(9, now);
                    
                    int affected = ps.executeUpdate();
                    
                    if (affected > 0) {
                        result.put("success", true);
                        result.put("message", "测试提醒创建成功");
                        result.put("triggerTime", triggerTime.toString());
                        result.put("reminderTime", reminderTime.toString());
                        result.put("userId", userId);
                        
                        log.info("创建测试提醒成功: userId={}, triggerTime={}, reminderTime={}", 
                                userId, triggerTime, reminderTime);
                    } else {
                        result.put("success", false);
                        result.put("message", "创建测试提醒失败");
                    }
                }
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("创建测试提醒失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 清理测试提醒
     */
    @DeleteMapping("/clear-test-reminders")
    public ResponseEntity<Map<String, Object>> clearTestReminders() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            try (Connection conn = dataSource.getConnection()) {
                String sql = "DELETE FROM todo_reminders WHERE message_id = -1";
                try (PreparedStatement ps = conn.prepareStatement(sql)) {
                    int affected = ps.executeUpdate();
                    
                    result.put("success", true);
                    result.put("message", "测试提醒清理完成");
                    result.put("deletedCount", affected);
                    
                    log.info("清理了{}个测试提醒", affected);
                }
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("清理测试提醒失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 手动触发提醒扫描（用于测试）
     */
    @PostMapping("/trigger-scan")
    public ResponseEntity<Map<String, Object>> triggerScan() {
        Map<String, Object> result = new HashMap<>();

        try {
            LocalDateTime now = LocalDateTime.now();
            result.put("scanTime", now.toString());

            // 直接查询数据库来模拟扫描逻辑
            try (Connection conn = dataSource.getConnection()) {
                String sql = "SELECT id, todo_content, reminder_time, trigger_time, user_open_id FROM todo_reminders WHERE status = 'PENDING' AND reminder_time <= ? ORDER BY reminder_time";
                try (PreparedStatement ps = conn.prepareStatement(sql)) {
                    ps.setObject(1, now);
                    try (ResultSet rs = ps.executeQuery()) {
                        StringBuilder details = new StringBuilder();
                        int count = 0;
                        while (rs.next()) {
                            count++;
                            details.append(String.format("ID:%d, 内容:%s, 提醒时间:%s, 触发时间:%s, 用户:%s\n",
                                rs.getLong("id"),
                                rs.getString("todo_content"),
                                rs.getObject("reminder_time"),
                                rs.getObject("trigger_time"),
                                rs.getString("user_open_id")
                            ));
                        }
                        result.put("foundReminders", count);
                        result.put("reminderDetails", details.toString());
                    }
                }
            }

            log.info("手动触发扫描完成: {}", result);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("手动触发扫描失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 测试语音识别接口
     */
    @GetMapping("/test-speech")
    public ResponseEntity<Map<String, Object>> testSpeechRecognition() {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("speechApiUrl", "/api/speech/recognize");
            result.put("supportedFormats", "百度API支持: pcm, wav, amr, m4a");
            result.put("recommendedFormat", "m4a (AAC编码, 16000Hz采样率)");
            result.put("maxFileSize", "10MB");
            result.put("maxDuration", "60秒");
            result.put("testMode", "当前使用测试模式，返回模拟结果");
            result.put("message", "语音识别接口已就绪");

            // 添加格式说明
            Map<String, String> formatDetails = new HashMap<>();
            formatDetails.put("pcm", "16000Hz, 16bit, 单声道, 无压缩");
            formatDetails.put("wav", "16000Hz, 16bit, 单声道, 无压缩");
            formatDetails.put("amr", "8000Hz, 单声道, 压缩格式");
            formatDetails.put("m4a", "16000Hz, 单声道, AAC压缩");
            result.put("formatDetails", formatDetails);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
