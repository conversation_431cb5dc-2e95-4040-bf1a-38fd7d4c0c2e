package com.wedin.controller;

import com.wedin.service.JPushService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 极光推送控制器
 */
@RestController
@RequestMapping("/api/push")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("isAuthenticated()")
@Tag(name = "推送管理", description = "极光推送相关接口")
@SecurityRequirement(name = "bearerAuth")
public class JPushController {

    private final JPushService jpushService;

    /**
     * 获取推送服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            boolean serviceAvailable = jpushService.isServiceAvailable();
            
            Map<String, Object> response = new HashMap<>();
            response.put("serviceAvailable", serviceAvailable);
            response.put("pushType", "JPush");
            response.put("message", serviceAvailable ? "极光推送服务正常" : "极光推送服务不可用");
            
            log.info("极光推送服务状态查询: serviceAvailable={}", serviceAvailable);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取极光推送服务状态失败: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("serviceAvailable", false);
            response.put("pushType", "JPush");
            response.put("message", "获取服务状态失败: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 发送测试推送通知
     */
    @Operation(summary = "发送测试推送", description = "向指定用户发送测试推送通知")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "推送发送成功"),
            @ApiResponse(responseCode = "400", description = "推送发送失败"),
            @ApiResponse(responseCode = "401", description = "未认证")
    })
    @PostMapping("/test/{userId}")
    public ResponseEntity<Map<String, Object>> sendTestNotification(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        try {
            log.info("发送极光推送测试推送通知: userId={}", userId);
            
            boolean success = jpushService.sendTestNotification(userId);
            long deviceCount = jpushService.getUserDeviceCount(userId);
            boolean serviceAvailable = jpushService.isServiceAvailable();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("deviceCount", deviceCount);
            response.put("serviceAvailable", serviceAvailable);
            response.put("message", success ? "测试推送发送成功" : "测试推送发送失败，请检查设备注册状态");
            
            log.info("极光推送测试推送完成: userId={}, success={}, deviceCount={}", 
                    userId, success, deviceCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("发送极光推送测试推送失败: userId={}, error={}", userId, e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("deviceCount", 0);
            response.put("serviceAvailable", false);
            response.put("message", "发送测试推送失败: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 检查用户是否有可用的推送设备
     */
    @GetMapping("/check/{userId}")
    public ResponseEntity<Map<String, Object>> checkUserPushAvailability(@PathVariable String userId) {
        try {
            long deviceCount = jpushService.getUserDeviceCount(userId);
            boolean serviceAvailable = jpushService.isServiceAvailable();
            boolean canReceivePush = deviceCount > 0 && serviceAvailable;
            
            Map<String, Object> response = new HashMap<>();
            response.put("userId", userId);
            response.put("canReceivePush", canReceivePush);
            response.put("deviceCount", deviceCount);
            response.put("serviceAvailable", serviceAvailable);
            response.put("message", canReceivePush ? 
                "用户可以接收极光推送通知" : 
                "用户无法接收极光推送通知，请检查设备注册状态或极光推送服务配置");
            
            log.info("检查用户极光推送可用性: userId={}, canReceivePush={}, deviceCount={}", 
                    userId, canReceivePush, deviceCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查用户极光推送可用性失败: userId={}, error={}", userId, e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("userId", userId);
            response.put("canReceivePush", false);
            response.put("deviceCount", 0);
            response.put("serviceAvailable", false);
            response.put("message", "检查推送可用性失败: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 发送自定义推送消息
     */
    @PostMapping("/custom")
    public ResponseEntity<Map<String, Object>> sendCustomNotification(
            @RequestBody Map<String, Object> request) {
        try {
            String userId = (String) request.get("userId");
            String title = (String) request.get("title");
            String content = (String) request.get("content");
            
            if (userId == null || title == null || content == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "缺少必要参数: userId, title, content");
                return ResponseEntity.badRequest().body(response);
            }
            
            log.info("发送极光推送自定义通知: userId={}, title={}, content={}", userId, title, content);
            
            // 这里可以扩展为发送自定义通知的逻辑
            // 目前使用测试推送的方式
            boolean success = jpushService.sendTestNotification(userId);
            long deviceCount = jpushService.getUserDeviceCount(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("deviceCount", deviceCount);
            response.put("message", success ? "自定义推送发送成功" : "自定义推送发送失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("发送极光推送自定义通知失败: error={}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "发送自定义推送失败: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }
}
