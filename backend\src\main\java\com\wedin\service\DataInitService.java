package com.wedin.service;

import com.wedin.entity.User;
import com.wedin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 数据初始化服务
 * 在应用启动时创建默认用户
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initDefaultUsers();
    }

    /**
     * 初始化默认用户
     * 已禁用：不再创建任何默认用户，请手动创建安全用户
     */
    private void initDefaultUsers() {
        log.info("默认用户创建已禁用。请手动创建安全用户。");
        log.info("创建用户指南：请参考 CREATE-SECURE-USERS.md 文档");

        // 检查是否存在用户，如果没有用户则给出提示
        long userCount = userRepository.count();
        if (userCount == 0) {
            log.warn("⚠️ 数据库中没有用户！请立即手动创建管理员用户。");
            log.warn("⚠️ 创建方法：");
            log.warn("⚠️ 1. 使用 BCrypt 在线工具生成密码哈希");
            log.warn("⚠️ 2. 直接在数据库中插入用户记录");
            log.warn("⚠️ 3. 参考 CREATE-SECURE-USERS.md 文档");
        } else {
            log.info("数据库中已有 {} 个用户", userCount);
        }
    }
}
