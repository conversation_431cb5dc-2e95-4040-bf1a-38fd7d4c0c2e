package com.wedin.controller;

import com.wedin.dto.*;
import com.wedin.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;

    /**
     * 用户注册
     */
    @Operation(summary = "用户注册", description = "注册新用户账号")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "注册成功"),
            @ApiResponse(responseCode = "400", description = "注册失败，用户名或邮箱已存在")
    })
    @PostMapping("/register")
    public ResponseEntity<com.wedin.dto.ApiResponse<AuthResponse>> register(@Valid @RequestBody RegisterRequest request) {
        try {
            log.info("收到注册请求: username={}", request.getUsername());
            
            AuthResponse response = authService.register(request);
            
            return ResponseEntity.ok(new com.wedin.dto.ApiResponse<>(200, "注册成功", response));
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new com.wedin.dto.ApiResponse<>(400, "注册失败: " + e.getMessage(), null));
        }
    }

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "使用用户名和密码登录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功"),
            @ApiResponse(responseCode = "400", description = "登录失败，用户名或密码错误")
    })
    @PostMapping("/login")
    public ResponseEntity<com.wedin.dto.ApiResponse<AuthResponse>> login(@Valid @RequestBody AuthRequest request) {
        try {
            log.info("收到登录请求: username={}", request.getUsername());
            
            AuthResponse response = authService.login(request);
            
            return ResponseEntity.ok(new com.wedin.dto.ApiResponse<>(200, "登录成功", response));
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new com.wedin.dto.ApiResponse<>(400, "登录失败: " + e.getMessage(), null));
        }
    }

    /**
     * 刷新token
     */
    @Operation(summary = "刷新访问令牌", description = "使用刷新令牌获取新的访问令牌")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "令牌刷新成功"),
            @ApiResponse(responseCode = "400", description = "刷新失败，刷新令牌无效或已过期")
    })
    @PostMapping("/refresh")
    public ResponseEntity<com.wedin.dto.ApiResponse<AuthResponse>> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            log.info("收到token刷新请求");
            
            AuthResponse response = authService.refreshToken(request);
            
            return ResponseEntity.ok(new com.wedin.dto.ApiResponse<>(200, "Token刷新成功", response));
        } catch (Exception e) {
            log.error("Token刷新失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new com.wedin.dto.ApiResponse<>(400, "Token刷新失败: " + e.getMessage(), null));
        }
    }

    /**
     * 登出（客户端删除token即可）
     */
    @Operation(summary = "用户登出", description = "登出当前用户（客户端需删除本地token）")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登出成功")
    })
    @PostMapping("/logout")
    public ResponseEntity<com.wedin.dto.ApiResponse<String>> logout() {
        log.info("用户登出");
        return ResponseEntity.ok(new com.wedin.dto.ApiResponse<>(200, "登出成功", "请删除本地存储的token"));
    }
}
