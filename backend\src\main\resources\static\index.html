<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WedIn AI助手 - 后端服务</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 300;
        }
        .subtitle {
            text-align: center;
            margin-bottom: 40px;
            opacity: 0.8;
            font-size: 1.1em;
        }
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .status.online {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .link-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .link-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .link-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.3em;
        }
        .link-card p {
            margin: 0;
            opacity: 0.8;
            font-size: 0.9em;
        }
        .icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.6;
            font-size: 0.9em;
        }
        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 2em;
            }
            .links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 WedIn AI助手</h1>
        <p class="subtitle">智能对话助手后端服务</p>
        
        <div class="status online">
            <h3>✅ 服务运行中</h3>
            <p>后端API服务正常运行</p>
        </div>

        <div class="links">
            <a href="/wedin/swagger-ui/index.html" class="link-card" target="_blank">
                <span class="icon">📖</span>
                <h3>API 文档</h3>
                <p>查看完整的API接口文档，支持在线测试</p>
            </a>

            <a href="/wedin/admin.html" class="link-card">
                <span class="icon">🔧</span>
                <h3>管理面板</h3>
                <p>系统管理和调试工具（需要登录）</p>
            </a>

            <a href="/wedin/actuator/health" class="link-card" target="_blank">
                <span class="icon">💚</span>
                <h3>健康检查</h3>
                <p>查看服务健康状态</p>
            </a>

            <a href="https://github.com/your-repo/wedin" class="link-card" target="_blank">
                <span class="icon">📱</span>
                <h3>Android 应用</h3>
                <p>下载移动端应用</p>
            </a>
        </div>

        <div class="footer">
            <p>WedIn AI助手 v1.0.0 | 基于Spring Boot 3.2.0构建</p>
            <p>支持JWT认证、消息处理、推送通知等功能</p>
        </div>
    </div>

    <script>
        // 简单的状态检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('WedIn AI助手后端服务已加载');
            
            // 可以添加更多的状态检查逻辑
            fetch('/wedin/actuator/health')
                .then(response => response.json())
                .then(data => {
                    console.log('健康检查:', data);
                })
                .catch(error => {
                    console.warn('健康检查失败:', error);
                });
        });
    </script>
</body>
</html>
