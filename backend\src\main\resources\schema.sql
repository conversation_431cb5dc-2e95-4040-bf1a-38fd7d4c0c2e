-- WedIn AI Assistant 数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wedin_ai
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE wedin_ai;

-- 创建消息表
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT COMMENT '用户ID',
    user_identifier VARCHAR(100) COMMENT '用户标识（兼容字符串ID）',
    wechat_msg_id VARCHAR(100) COMMENT '微信消息ID（用于防重复）',
    type VARCHAR(20) DEFAULT 'TEXT' COMMENT '消息类型',
    content TEXT COMMENT '原始消息内容',
    ai_summary TEXT COMMENT 'AI理解后的内容摘要',
    category VARCHAR(50) COMMENT 'AI分类结果',
    keywords VARCHAR(500) COMMENT 'AI提取的关键词',
    importance_score INT COMMENT '重要性评分(1-10)',
    need_reminder BOOLEAN DEFAULT FALSE COMMENT '是否需要提醒',
    reminder_time DATETIME COMMENT '提醒时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    ai_status VARCHAR(20) DEFAULT 'PENDING' COMMENT 'AI处理状态',
    ai_response TEXT COMMENT 'AI回复内容（PWA版本）',
    tags VARCHAR(500) COMMENT '标签（PWA版本）',
    importance INT COMMENT '重要程度（PWA版本，1-5星）',
    conversation_id VARCHAR(100) COMMENT '对话会话ID',
    parent_message_id BIGINT COMMENT '父消息ID（用于确认流程）',
    confirmation_status VARCHAR(20) COMMENT '确认状态',
    is_from_user BOOLEAN COMMENT '是否来自用户',
    is_from_ai BOOLEAN COMMENT '是否来自AI',

    INDEX idx_user_id (user_id),
    INDEX idx_user_identifier (user_identifier),
    INDEX idx_wechat_msg_id (wechat_msg_id),
    INDEX idx_created_at (created_at),
    INDEX idx_category (category),
    INDEX idx_importance_score (importance_score),
    INDEX idx_reminder_time (reminder_time),
    INDEX idx_ai_status (ai_status),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_parent_message_id (parent_message_id),
    INDEX idx_confirmation_status (confirmation_status),
    FULLTEXT INDEX idx_content (content),
    FULLTEXT INDEX idx_ai_summary (ai_summary),
    FULLTEXT INDEX idx_keywords (keywords),

    -- 微信消息ID唯一约束（防止重复消息）
    CONSTRAINT uk_wechat_msg_id UNIQUE (wechat_msg_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 清理重复的微信消息ID（如果存在）
-- 删除重复记录，保留最早的记录
-- DELETE m1 FROM messages m1
-- INNER JOIN messages m2
-- WHERE m1.wechat_msg_id = m2.wechat_msg_id
--   AND m1.wechat_msg_id IS NOT NULL
--   AND m1.id > m2.id;

-- 创建视图：用户消息统计视图
CREATE OR REPLACE VIEW v_user_message_stats AS
SELECT
    user_identifier,
    COUNT(*) as total_messages,
    COUNT(CASE WHEN category = 'TODO' THEN 1 END) as todo_count,
    COUNT(CASE WHEN category = 'IMPORTANT_INFO' THEN 1 END) as important_info_count,
    COUNT(CASE WHEN category = 'WORK_RELATED' THEN 1 END) as work_related_count,
    COUNT(CASE WHEN category = 'LEARNING_NOTE' THEN 1 END) as learning_note_count,
    COUNT(CASE WHEN need_reminder = TRUE THEN 1 END) as reminder_count,
    AVG(importance_score) as avg_importance_score,
    MAX(created_at) as last_message_time,
    MIN(created_at) as first_message_time
FROM messages
GROUP BY user_identifier;

-- 创建待办事项提醒表
CREATE TABLE IF NOT EXISTS todo_reminders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_id BIGINT NOT NULL,
    user_open_id VARCHAR(100) NOT NULL,
    todo_content TEXT NOT NULL,
    trigger_time DATETIME NOT NULL,
    reminder_time DATETIME NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    reminder_count INT NOT NULL DEFAULT 0,
    last_reminder_time DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_message_id (message_id),
    INDEX idx_user_open_id (user_open_id),
    INDEX idx_status (status),
    INDEX idx_reminder_time (reminder_time),
    INDEX idx_trigger_time (trigger_time),
    INDEX idx_user_status (user_open_id, status),

    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建存储过程：清理过期消息（可选）
DELIMITER //
CREATE PROCEDURE CleanExpiredMessages(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;

    -- 删除指定天数之前的消息（保留重要消息）
    DELETE FROM messages
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND (importance_score IS NULL OR importance_score < 7)
    AND (need_reminder IS NULL OR need_reminder = FALSE);

    SET affected_rows = ROW_COUNT();

    SELECT CONCAT('清理了 ', affected_rows, ' 条过期消息') as result;
END //
DELIMITER ;

-- 创建存储过程：清理过期提醒
DELIMITER //
CREATE PROCEDURE CleanExpiredReminders(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;

    -- 将过期的提醒标记为EXPIRED
    UPDATE todo_reminders
    SET status = 'EXPIRED', updated_at = NOW()
    WHERE status = 'PENDING'
    AND trigger_time < DATE_SUB(NOW(), INTERVAL 1 DAY);

    SET affected_rows = ROW_COUNT();

    -- 删除很久之前的已完成或已过期提醒
    DELETE FROM todo_reminders
    WHERE status IN ('COMPLETED', 'EXPIRED', 'CANCELLED')
    AND updated_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);

    SET affected_rows = affected_rows + ROW_COUNT();

    SELECT CONCAT('清理了 ', affected_rows, ' 条过期提醒') as result;
END //
DELIMITER ;

-- 创建设备token表
CREATE TABLE IF NOT EXISTS device_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    token TEXT NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    platform VARCHAR(50),
    device_id VARCHAR(200),
    app_version VARCHAR(50),
    os_version VARCHAR(50),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    registered_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    failure_count INT NOT NULL DEFAULT 0,
    last_push_at DATETIME,
    remarks TEXT,

    INDEX idx_user_id (user_id),
    INDEX idx_device_type (device_type),
    INDEX idx_status (status),
    INDEX idx_user_device_status (user_id, device_type, status),
    INDEX idx_token_hash (token(255)),
    INDEX idx_registered_at (registered_at),
    INDEX idx_last_active_at (last_active_at),
    INDEX idx_failure_count (failure_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
