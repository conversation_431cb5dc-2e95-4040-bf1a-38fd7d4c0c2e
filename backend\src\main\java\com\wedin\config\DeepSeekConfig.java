package com.wedin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * DeepSeek API配置
 */
@Configuration
@ConfigurationProperties(prefix = "deepseek")
@Data
public class DeepSeekConfig {

    /**
     * DeepSeek API密钥
     */
    private String apiKey;

    /**
     * DeepSeek API基础URL
     */
    private String baseUrl;

    /**
     * 使用的模型名称
     */
    private String model;

    /**
     * 最大token数
     */
    private Integer maxTokens;

    /**
     * 温度参数
     */
    private Double temperature;
}
