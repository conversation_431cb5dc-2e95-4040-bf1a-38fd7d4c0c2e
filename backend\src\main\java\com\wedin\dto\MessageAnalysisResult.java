package com.wedin.dto;

import com.wedin.entity.MessageCategory;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI消息分析结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MessageAnalysisResult {

    /**
     * 消息摘要
     */
    private String summary;

    /**
     * 消息分类
     */
    private MessageCategory category;

    /**
     * 关键词列表
     */
    private List<String> keywords;

    /**
     * 重要性评分 (1-10)
     */
    private Integer importanceScore;

    /**
     * 是否需要提醒
     */
    private Boolean needReminder;

    /**
     * 建议提醒时间
     */
    private LocalDateTime suggestedReminderTime;

    /**
     * 分析置信度 (0-1)
     */
    private Double confidence;

    /**
     * AI分析的详细说明
     */
    private String analysisNote;
}
