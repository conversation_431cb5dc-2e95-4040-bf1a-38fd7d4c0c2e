package com.wedin.controller;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Server-Sent Events 控制器
 * 用于实时推送消息处理状态更新
 */
@RestController
@RequestMapping("/api/sse")
@CrossOrigin(origins = {"http://localhost:3001", "http://localhost:3000"}, allowCredentials = "false")
public class SseController {

    private static final Logger logger = LoggerFactory.getLogger(SseController.class);
    
    // 存储所有活跃的SSE连接
    private static final ConcurrentHashMap<String, CopyOnWriteArrayList<SseEmitter>> connections = new ConcurrentHashMap<>();
    
    // SSE连接超时时间（5分钟）
    private static final long SSE_TIMEOUT = 5 * 60 * 1000L;

    /**
     * 建立SSE连接
     * @param userId 用户ID
     * @return SseEmitter
     */
    @GetMapping(value = "/connect/{userId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@PathVariable String userId) {
        logger.info("用户 {} 建立SSE连接", userId);
        
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
        
        // 获取或创建用户的连接列表
        connections.computeIfAbsent(userId, k -> new CopyOnWriteArrayList<>()).add(emitter);
        
        // 设置连接完成时的清理逻辑
        emitter.onCompletion(() -> {
            logger.info("用户 {} 的SSE连接完成", userId);
            removeConnection(userId, emitter);
        });
        
        // 设置连接超时时的清理逻辑
        emitter.onTimeout(() -> {
            logger.info("用户 {} 的SSE连接超时", userId);
            removeConnection(userId, emitter);
        });
        
        // 设置连接错误时的清理逻辑
        emitter.onError((ex) -> {
            logger.error("用户 {} 的SSE连接发生错误: {}", userId, ex.getMessage());
            removeConnection(userId, emitter);
        });
        
        try {
            // 发送连接成功事件
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("SSE连接建立成功"));
        } catch (IOException e) {
            logger.error("发送连接成功事件失败: {}", e.getMessage());
            removeConnection(userId, emitter);
        }
        
        return emitter;
    }

    /**
     * 推送消息处理完成事件
     * @param userId 用户ID
     * @param messageId 消息ID
     * @param message 完整的消息对象
     */
    public static void pushMessageCompleted(String userId, Long messageId, Object message) {
        logger.info("推送消息完成事件: userId={}, messageId={}", userId, messageId);
        
        CopyOnWriteArrayList<SseEmitter> userConnections = connections.get(userId);
        if (userConnections == null || userConnections.isEmpty()) {
            logger.debug("用户 {} 没有活跃的SSE连接", userId);
            return;
        }
        
        // 创建事件数据
        MessageCompletedEvent event = new MessageCompletedEvent(messageId, message);
        
        // 向所有用户连接推送事件
        userConnections.removeIf(emitter -> {
            try {
                emitter.send(SseEmitter.event()
                        .name("messageCompleted")
                        .data(event));
                return false; // 发送成功，保留连接
            } catch (IOException e) {
                logger.warn("向用户 {} 推送事件失败: {}", userId, e.getMessage());
                return true; // 发送失败，移除连接
            }
        });
    }

    /**
     * 推送消息处理失败事件
     * @param userId 用户ID
     * @param messageId 消息ID
     * @param error 错误信息
     */
    public static void pushMessageFailed(String userId, Long messageId, String error) {
        logger.info("推送消息失败事件: userId={}, messageId={}, error={}", userId, messageId, error);
        
        CopyOnWriteArrayList<SseEmitter> userConnections = connections.get(userId);
        if (userConnections == null || userConnections.isEmpty()) {
            return;
        }
        
        MessageFailedEvent event = new MessageFailedEvent(messageId, error);
        
        userConnections.removeIf(emitter -> {
            try {
                emitter.send(SseEmitter.event()
                        .name("messageFailed")
                        .data(event));
                return false;
            } catch (IOException e) {
                logger.warn("向用户 {} 推送失败事件失败: {}", userId, e.getMessage());
                return true;
            }
        });
    }

    /**
     * 移除SSE连接
     */
    private void removeConnection(String userId, SseEmitter emitter) {
        CopyOnWriteArrayList<SseEmitter> userConnections = connections.get(userId);
        if (userConnections != null) {
            userConnections.remove(emitter);
            if (userConnections.isEmpty()) {
                connections.remove(userId);
                logger.info("用户 {} 的所有SSE连接已清理", userId);
            }
        }
    }

    /**
     * 测试端点
     */
    @GetMapping("/test")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("SSE Controller is working!");
    }

    /**
     * 获取连接状态
     */
    @GetMapping("/status")
    public ResponseEntity<ConnectionStatus> getStatus() {
        int totalConnections = connections.values().stream()
                .mapToInt(CopyOnWriteArrayList::size)
                .sum();

        return ResponseEntity.ok(new ConnectionStatus(connections.size(), totalConnections));
    }

    // 事件数据类
    public static class MessageCompletedEvent {
        private Long messageId;
        private Object message;

        public MessageCompletedEvent(Long messageId, Object message) {
            this.messageId = messageId;
            this.message = message;
        }

        // Getters
        public Long getMessageId() { return messageId; }
        public Object getMessage() { return message; }
    }

    public static class MessageFailedEvent {
        private Long messageId;
        private String error;

        public MessageFailedEvent(Long messageId, String error) {
            this.messageId = messageId;
            this.error = error;
        }

        // Getters
        public Long getMessageId() { return messageId; }
        public String getError() { return error; }
    }

    public static class ConnectionStatus {
        private int activeUsers;
        private int totalConnections;

        public ConnectionStatus(int activeUsers, int totalConnections) {
            this.activeUsers = activeUsers;
            this.totalConnections = totalConnections;
        }

        // Getters
        public int getActiveUsers() { return activeUsers; }
        public int getTotalConnections() { return totalConnections; }
    }
}
