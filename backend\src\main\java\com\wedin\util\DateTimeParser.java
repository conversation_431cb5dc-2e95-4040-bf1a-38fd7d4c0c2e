package com.wedin.util;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 中文日期时间解析工具类
 * 用于解析用户输入的中文时间表达式
 */
@Slf4j
public class DateTimeParser {

    /**
     * 解析中文时间表达式
     * @param timeExpression 时间表达式，如"晚上7点"、"明天下午3点"等
     * @param baseTime 基准时间，默认为当前时间
     * @return 解析后的时间，如果解析失败返回null
     */
    public static LocalDateTime parseChineseTimeExpression(String timeExpression, LocalDateTime baseTime) {
        if (timeExpression == null || timeExpression.trim().isEmpty()) {
            return null;
        }
        
        if (baseTime == null) {
            baseTime = LocalDateTime.now();
        }
        
        String cleanExpression = timeExpression.trim().toLowerCase();
        log.debug("解析时间表达式: {}, 基准时间: {}", cleanExpression, baseTime);
        
        try {
            // 1. 尝试解析相对日期 + 时间
            LocalDateTime result = parseRelativeDateWithTime(cleanExpression, baseTime);
            if (result != null) {
                log.debug("解析结果: {}", result);
                return result;
            }
            
            // 2. 尝试解析今天的时间
            result = parseTodayTime(cleanExpression, baseTime);
            if (result != null) {
                log.debug("解析结果: {}", result);
                return result;
            }
            
            // 3. 尝试解析纯时间表达式（默认今天，如果已过则明天）
            result = parseTimeOnly(cleanExpression, baseTime);
            if (result != null) {
                log.debug("解析结果: {}", result);
                return result;
            }
            
        } catch (Exception e) {
            log.warn("解析时间表达式失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 解析相对日期 + 时间，如"明天晚上7点"、"后天下午3点"、"下周一上午9点"
     */
    private static LocalDateTime parseRelativeDateWithTime(String expression, LocalDateTime baseTime) {
        // 明天 + 时间
        Pattern tomorrowPattern = Pattern.compile("明天|明日");
        if (tomorrowPattern.matcher(expression).find()) {
            LocalTime time = extractTime(expression);
            if (time != null) {
                return baseTime.plusDays(1).with(time);
            }
        }
        
        // 后天 + 时间
        Pattern dayAfterTomorrowPattern = Pattern.compile("后天");
        if (dayAfterTomorrowPattern.matcher(expression).find()) {
            LocalTime time = extractTime(expression);
            if (time != null) {
                return baseTime.plusDays(2).with(time);
            }
        }
        
        // 今天 + 时间
        Pattern todayPattern = Pattern.compile("今天|今日");
        if (todayPattern.matcher(expression).find()) {
            LocalTime time = extractTime(expression);
            if (time != null) {
                return baseTime.with(time);
            }
        }
        
        // 下周 + 时间
        Pattern nextWeekPattern = Pattern.compile("下周|下星期");
        if (nextWeekPattern.matcher(expression).find()) {
            LocalTime time = extractTime(expression);
            if (time != null) {
                // 默认下周一
                LocalDateTime nextWeek = baseTime.plusWeeks(1);
                // 找到下周一（周一是1）
                int daysToAdd = 1 - nextWeek.getDayOfWeek().getValue();
                if (daysToAdd <= 0) {
                    daysToAdd += 7;
                }
                return nextWeek.plusDays(daysToAdd).with(time);
            }
        }
        
        // 下个月 + 时间
        Pattern nextMonthPattern = Pattern.compile("下个月|下月");
        if (nextMonthPattern.matcher(expression).find()) {
            LocalTime time = extractTime(expression);
            if (time != null) {
                // 默认下个月1号
                return baseTime.plusMonths(1).withDayOfMonth(1).with(time);
            }
        }
        
        // 具体星期 + 时间，如"周一上午9点"、"星期二下午2点"
        Pattern weekdayPattern = Pattern.compile("(周|星期)(一|二|三|四|五|六|日|天|1|2|3|4|5|6|7)");
        Matcher weekdayMatcher = weekdayPattern.matcher(expression);
        if (weekdayMatcher.find()) {
            String dayStr = weekdayMatcher.group(2);
            int targetDayOfWeek = parseWeekday(dayStr);
            if (targetDayOfWeek > 0) {
                LocalTime time = extractTime(expression);
                if (time != null) {
                    // 计算到目标星期几需要的天数
                    int currentDayOfWeek = baseTime.getDayOfWeek().getValue();
                    int daysToAdd = targetDayOfWeek - currentDayOfWeek;
                    if (daysToAdd <= 0) {
                        daysToAdd += 7; // 如果是过去的时间，取下周的这一天
                    }
                    return baseTime.plusDays(daysToAdd).with(time);
                }
            }
        }
        
        return null;
    }
    
    /**
     * 解析星期几
     */
    private static int parseWeekday(String dayStr) {
        return switch (dayStr) {
            case "一", "1" -> 1;
            case "二", "2" -> 2;
            case "三", "3" -> 3;
            case "四", "4" -> 4;
            case "五", "5" -> 5;
            case "六", "6" -> 6;
            case "日", "天", "7" -> 7;
            default -> 0;
        };
    }
    
    /**
     * 解析今天的时间表达式
     */
    private static LocalDateTime parseTodayTime(String expression, LocalDateTime baseTime) {
        LocalTime time = extractTime(expression);
        if (time != null) {
            LocalDateTime result = baseTime.with(time);
            // 如果时间已经过了，返回明天的这个时间
            if (result.isBefore(baseTime)) {
                result = result.plusDays(1);
            }
            return result;
        }
        return null;
    }
    
    /**
     * 解析纯时间表达式
     */
    private static LocalDateTime parseTimeOnly(String expression, LocalDateTime baseTime) {
        LocalTime time = extractTime(expression);
        if (time != null) {
            LocalDateTime result = baseTime.with(time);
            // 如果时间已经过了，返回明天的这个时间
            if (result.isBefore(baseTime)) {
                result = result.plusDays(1);
            }
            return result;
        }
        return null;
    }
    
    /**
     * 从表达式中提取时间
     */
    private static LocalTime extractTime(String expression) {
        // 12小时制：上午/下午/中午/晚上 + 数字 + 点/时
        Pattern time12Pattern = Pattern.compile("(上午|下午|中午|晚上|夜里)\\s*(\\d{1,2})\\s*[点时]\\s*(\\d{1,2})?\\s*[分]?|" +
                                              "(上午|下午|中午|晚上|夜里)\\s*(\\d{1,2})\\s*[点时]\\s*半");
        Matcher matcher12 = time12Pattern.matcher(expression);
        if (matcher12.find()) {
            String period = matcher12.group(1) != null ? matcher12.group(1) : matcher12.group(4);
            int hour = Integer.parseInt(matcher12.group(2) != null ? matcher12.group(2) : matcher12.group(5));
            int minute = 0;
            
            if (matcher12.group(3) != null) {
                minute = Integer.parseInt(matcher12.group(3));
            } else if (expression.contains("半")) {
                minute = 30;
            }
            
            // 转换为24小时制
            if ("下午".equals(period) || "晚上".equals(period) || "夜里".equals(period)) {
                if (hour < 12) hour += 12;
            } else if ("上午".equals(period) && hour == 12) {
                hour = 0;
            } else if ("中午".equals(period)) {
                hour = 12;
            }
            
            if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
                return LocalTime.of(hour, minute);
            }
        }
        
        // 24小时制：数字 + 点/时 + 分钟（可选）
        Pattern time24Pattern = Pattern.compile("(\\d{1,2})\\s*[点时:]\\s*(\\d{1,2})?\\s*[分]?");
        Matcher matcher24 = time24Pattern.matcher(expression);
        if (matcher24.find()) {
            int hour = Integer.parseInt(matcher24.group(1));
            int minute = matcher24.group(2) != null ? Integer.parseInt(matcher24.group(2)) : 0;
            
            if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
                return LocalTime.of(hour, minute);
            }
        }
        
        // 纯数字时间：如"7点"、"19点"
        Pattern pureNumberPattern = Pattern.compile("(\\d{1,2})\\s*[点时]");
        Matcher pureNumberMatcher = pureNumberPattern.matcher(expression);
        if (pureNumberMatcher.find()) {
            int hour = Integer.parseInt(pureNumberMatcher.group(1));
            if (hour >= 0 && hour <= 23) {
                return LocalTime.of(hour, 0);
            }
        }
        
        return null;
    }
    
    /**
     * 格式化时间为字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 检查时间是否为未来时间
     */
    public static boolean isFutureTime(LocalDateTime dateTime) {
        return dateTime != null && dateTime.isAfter(LocalDateTime.now());
    }
    
    /**
     * 解析ISO日期时间字符串
     * @param isoString ISO格式的日期时间字符串，如"2025-07-14T22:46:35"
     * @return 解析后的LocalDateTime，如果解析失败返回null
     */
    public static LocalDateTime parseISODateTime(String isoString) {
        if (isoString == null || isoString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 处理不同的ISO格式
            String cleanString = isoString.trim();
            
            // 移除末尾的Z或时区信息
            if (cleanString.endsWith("Z")) {
                cleanString = cleanString.substring(0, cleanString.length() - 1);
            } else if (cleanString.contains("+") || cleanString.lastIndexOf("-") > 10) {
                // 移除时区信息
                int tzIndex = Math.max(cleanString.lastIndexOf("+"), cleanString.lastIndexOf("-"));
                if (tzIndex > 10) { // 确保不是日期部分的-
                    cleanString = cleanString.substring(0, tzIndex);
                }
            }
            
            // 尝试不同的格式
            DateTimeFormatter[] formatters = {
                DateTimeFormatter.ISO_LOCAL_DATE_TIME,
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm")
            };
            
            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalDateTime.parse(cleanString, formatter);
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }
            
            log.warn("无法解析ISO日期时间字符串: {}", isoString);
            return null;
            
        } catch (Exception e) {
            log.error("解析ISO日期时间字符串失败: {}, error: {}", isoString, e.getMessage());
            return null;
        }
    }
}
