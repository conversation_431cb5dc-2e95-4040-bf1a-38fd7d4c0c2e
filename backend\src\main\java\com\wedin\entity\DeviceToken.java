package com.wedin.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 设备推送token实体
 */
@Entity
@Table(name = "device_tokens")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户标识
     */
    @Column(name = "user_id", nullable = false, length = 100)
    private String userId;

    /**
     * 设备推送token
     */
    @Column(name = "token", nullable = false, columnDefinition = "TEXT")
    private String token;

    /**
     * 设备类型 (JPUSH, APNS, WEB_PUSH等)
     */
    @Column(name = "device_type", nullable = false, length = 50)
    private String deviceType;

    /**
     * 设备平台 (Android, iOS, Web等)
     */
    @Column(name = "platform", length = 50)
    private String platform;

    /**
     * 设备标识符
     */
    @Column(name = "device_id", length = 200)
    private String deviceId;

    /**
     * 应用版本
     */
    @Column(name = "app_version", length = 50)
    private String appVersion;

    /**
     * 系统版本
     */
    @Column(name = "os_version", length = 50)
    private String osVersion;

    /**
     * token状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private TokenStatus status = TokenStatus.ACTIVE;

    /**
     * 注册时间
     */
    @Column(name = "registered_at", nullable = false)
    private LocalDateTime registeredAt;

    /**
     * 最后活跃时间
     */
    @Column(name = "last_active_at")
    private LocalDateTime lastActiveAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 推送失败次数
     */
    @Column(name = "failure_count", nullable = false)
    @Builder.Default
    private Integer failureCount = 0;

    /**
     * 最后推送时间
     */
    @Column(name = "last_push_at")
    private LocalDateTime lastPushAt;

    /**
     * 备注信息
     */
    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    /**
     * token状态枚举
     */
    public enum TokenStatus {
        /**
         * 活跃状态
         */
        ACTIVE("活跃"),
        
        /**
         * 非活跃状态
         */
        INACTIVE("非活跃"),
        
        /**
         * 已过期
         */
        EXPIRED("已过期"),
        
        /**
         * 推送失败
         */
        FAILED("推送失败");

        private final String displayName;

        TokenStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (registeredAt == null) {
            registeredAt = LocalDateTime.now();
        }
        if (lastActiveAt == null) {
            lastActiveAt = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
