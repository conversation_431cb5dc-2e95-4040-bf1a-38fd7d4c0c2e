package com.wedin.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wedin.config.DeepSeekConfig;
import com.wedin.dto.DeepSeekRequest;
import com.wedin.dto.DeepSeekResponse;
import com.wedin.dto.MessageAnalysisResult;
import com.wedin.entity.MessageCategory;
import com.wedin.util.DateTimeParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * DeepSeek AI服务
 */
@Service
@Slf4j
public class DeepSeekService {

    private final DeepSeekConfig deepSeekConfig;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public DeepSeekService(DeepSeekConfig deepSeekConfig, ObjectMapper objectMapper) {
        this.deepSeekConfig = deepSeekConfig;
        this.objectMapper = objectMapper;
        this.webClient = WebClient.builder()
                .baseUrl(deepSeekConfig.getBaseUrl())
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + deepSeekConfig.getApiKey())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    /**
     * 分析消息内容
     */
    public MessageAnalysisResult analyzeMessage(String content) {
        try {
            // 先尝试本地时间解析
            LocalDateTime localParsedTime = DateTimeParser.parseChineseTimeExpression(content, LocalDateTime.now());

            String prompt = buildAnalysisPrompt(content);
            String response = callDeepSeekAPI(prompt);
            MessageAnalysisResult result = parseAnalysisResult(response);

            // 如果本地解析成功且AI解析的时间不合理，使用本地解析结果
            if (localParsedTime != null && DateTimeParser.isFutureTime(localParsedTime)) {
                if (result.getSuggestedReminderTime() == null ||
                    result.getSuggestedReminderTime().isBefore(LocalDateTime.now()) ||
                    result.getSuggestedReminderTime().getYear() < LocalDateTime.now().getYear()) {

                    log.info("使用本地时间解析结果: {} -> {}", content, localParsedTime);
                    result.setSuggestedReminderTime(localParsedTime);
                    result.setAnalysisNote(result.getAnalysisNote() + " (使用本地时间解析)");
                }
            }

            return result;
        } catch (Exception e) {
            log.error("分析消息失败: {}", e.getMessage(), e);
            return createDefaultAnalysisResult(content);
        }
    }

    /**
     * 搜索相关消息
     */
    public String searchMessages(String query, List<String> messageContents) {
        try {
            String prompt = buildSearchPrompt(query, messageContents);
            return callDeepSeekAPI(prompt);
        } catch (Exception e) {
            log.error("搜索消息失败: {}", e.getMessage(), e);
            return "抱歉，搜索过程中出现了问题，请稍后再试。";
        }
    }

    /**
     * 生成智能回复
     */
    public String generateSmartReply(String userMessage, MessageCategory category,
                                   List<String> recentMessages, Integer importanceScore) {
        try {
            String prompt = buildSmartReplyPrompt(userMessage, category, recentMessages, importanceScore);
            return callDeepSeekAPI(prompt);
        } catch (Exception e) {
            log.error("生成智能回复失败: {}", e.getMessage(), e);
            return generateDefaultReply(category, importanceScore);
        }
    }

    /**
     * 构建消息分析提示词
     */
    private String buildAnalysisPrompt(String content) {
        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
        String dayOfWeek = getDayOfWeekInChinese(now.getDayOfWeek());

        return String.format("""
            请分析以下用户消息，并以JSON格式返回分析结果：

            当前时间：%s (%s %s)
            时区：Asia/Shanghai (北京时间)

            用户消息："%s"

            请按以下格式返回JSON：
            {
                "summary": "消息的简要摘要",
                "category": "消息分类",
                "keywords": ["关键词1", "关键词2"],
                "importanceScore": 评分(1-10),
                "needReminder": true/false,
                "suggestedReminderTime": "YYYY-MM-DD HH:mm:ss",
                "confidence": 置信度(0-1),
                "analysisNote": "分析说明"
            }

            分类选项：
            - TODO: 待办事项（包含"提醒我"、"记得"、"别忘了"、"要做"等表达的任务或提醒）
            - IMPORTANT_INFO: 重要信息
            - CONTACT_INFO: 联系人信息
            - LEARNING_NOTE: 学习笔记
            - PERSONAL_THOUGHT: 个人想法
            - FINANCIAL: 财务相关
            - HEALTH: 健康相关
            - ENTERTAINMENT: 娱乐休闲
            - OTHER: 其他

            重要分类规则：
            1. 如果消息包含"提醒我"、"记得"、"别忘了"、"要做"、"需要做"等词汇，优先分类为TODO
            2. 即使内容涉及健康、工作等其他领域，只要是请求提醒的，都应该分类为TODO
            3. 例如："提醒我喝水"、"记得吃药"、"别忘了锻炼" 都应该分类为TODO

            时间解析规则：
            1. 如果消息包含时间信息且需要提醒，请基于当前时间设置合理的提醒时间
            2. 相对时间解析：
               - "今天"、"今日" = 当前日期
               - "明天"、"明日" = 当前日期+1天
               - "后天" = 当前日期+2天
               - "下周" = 下周一
               - "晚上"、"夜里" = 当天19:00-23:59
               - "上午" = 当天06:00-11:59
               - "下午" = 当天12:00-18:59
               - "中午" = 当天12:00
            3. 如果只提到时间点（如"7点"），默认指当天，如果当天该时间已过，则指明天
            4. 重要性评分基于内容的紧急程度和重要性
            5. 关键词应该是最能代表消息内容的词汇
            6. 提醒时间必须是未来时间，不能是过去时间
            """, currentTime, currentDate, dayOfWeek, content);
    }

    /**
     * 获取中文星期
     */
    private String getDayOfWeekInChinese(java.time.DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> "星期一";
            case TUESDAY -> "星期二";
            case WEDNESDAY -> "星期三";
            case THURSDAY -> "星期四";
            case FRIDAY -> "星期五";
            case SATURDAY -> "星期六";
            case SUNDAY -> "星期日";
        };
    }

    /**
     * 构建搜索提示词
     */
    private String buildSearchPrompt(String query, List<String> messageContents) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("用户想要搜索：").append(query).append("\n\n");
        prompt.append("以下是用户的历史消息：\n");

        for (int i = 0; i < messageContents.size() && i < 20; i++) {
            prompt.append(i + 1).append(". ").append(messageContents.get(i)).append("\n");
        }

        prompt.append("\n请根据用户的搜索需求，从上述消息中找出最相关的内容，并以友好的方式回复用户。");
        prompt.append("如果没有找到相关内容，请礼貌地告知用户。");

        return prompt.toString();
    }

    /**
     * 构建智能回复提示词
     */
    private String buildSmartReplyPrompt(String userMessage, MessageCategory category,
                                       List<String> recentMessages, Integer importanceScore) {
        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个智能助理，需要根据用户的消息内容生成合适的回复。\n\n");

        prompt.append("当前时间：").append(currentTime).append("\n");
        prompt.append("用户最新消息：\"").append(userMessage).append("\"\n");
        prompt.append("消息分类：").append(category.getDisplayName()).append("\n");
        prompt.append("重要性评分：").append(importanceScore).append("/10\n\n");

        if (recentMessages != null && !recentMessages.isEmpty()) {
            prompt.append("用户最近的消息历史：\n");
            for (int i = 0; i < Math.min(recentMessages.size(), 5); i++) {
                prompt.append("- ").append(recentMessages.get(i)).append("\n");
            }
            prompt.append("\n");
        }

        prompt.append("请根据以下规则生成回复：\n");
        prompt.append("1. 回复要简洁、友好、有用\n");
        prompt.append("2. 根据消息类别提供相应的建议或确认\n");
        prompt.append("3. 对于重要消息（评分≥7）要表示重视\n");
        prompt.append("4. 对于待办事项要提供提醒确认\n");
        prompt.append("5. 对于学习笔记要给予鼓励\n");
        prompt.append("6. 对于工作相关要表示理解和支持\n");
        prompt.append("7. 回复长度控制在50字以内\n");
        prompt.append("8. 使用温暖、专业的语调\n\n");

        prompt.append("请直接返回回复内容，不要包含其他说明。");

        return prompt.toString();
    }

    /**
     * 生成默认回复
     */
    private String generateDefaultReply(MessageCategory category, Integer importanceScore) {
        if (category == null) {
            return "收到您的消息，我会帮您记录下来。";
        }

        return switch (category) {
            case TODO -> "好的，我已经记录了这个待办事项" +
                        (importanceScore >= 7 ? "，这看起来很重要，请注意及时处理。" : "。");
            case IMPORTANT_INFO -> "重要信息已记录" +
                                 (importanceScore >= 8 ? "，我会特别关注这条信息。" : "。");
            case CONTACT_INFO -> "联系信息已保存。";
            case LEARNING_NOTE -> "学习笔记已记录，持续学习很棒！";
            case PERSONAL_THOUGHT -> "您的想法已记录下来。";
            case FINANCIAL -> "财务信息已安全记录。";
            case HEALTH -> "健康相关信息已记录，注意身体健康。";
            case ENTERTAINMENT -> "娱乐信息已记录，劳逸结合很重要。";
            default -> "消息已收到并记录。";
        };
    }

    /**
     * 调用DeepSeek API
     */
    private String callDeepSeekAPI(String prompt) {
        DeepSeekRequest request = DeepSeekRequest.builder()
                .model(deepSeekConfig.getModel())
                .maxTokens(deepSeekConfig.getMaxTokens())
                .temperature(deepSeekConfig.getTemperature())
                .stream(false)
                .messages(List.of(
                        DeepSeekRequest.ChatMessage.builder()
                                .role("user")
                                .content(prompt)
                                .build()
                ))
                .build();

        try {
            Mono<DeepSeekResponse> responseMono = webClient.post()
                    .uri("/chat/completions")
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(DeepSeekResponse.class);

            DeepSeekResponse response = responseMono.block();
            
            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                return response.getChoices().get(0).getMessage().getContent();
            }
            
            throw new RuntimeException("DeepSeek API返回空响应");
            
        } catch (Exception e) {
            log.error("调用DeepSeek API失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用DeepSeek API失败", e);
        }
    }

    /**
     * 解析分析结果
     */
    private MessageAnalysisResult parseAnalysisResult(String response) {
        try {
            // 提取JSON部分
            String jsonStr = extractJsonFromResponse(response);
            
            // 解析JSON
            var jsonNode = objectMapper.readTree(jsonStr);
            
            return MessageAnalysisResult.builder()
                    .summary(jsonNode.get("summary").asText())
                    .category(parseCategory(jsonNode.get("category").asText()))
                    .keywords(parseKeywords(jsonNode.get("keywords")))
                    .importanceScore(jsonNode.get("importanceScore").asInt())
                    .needReminder(jsonNode.get("needReminder").asBoolean())
                    .suggestedReminderTime(parseDateTime(jsonNode.get("suggestedReminderTime")))
                    .confidence(jsonNode.get("confidence").asDouble())
                    .analysisNote(jsonNode.get("analysisNote").asText())
                    .build();
                    
        } catch (Exception e) {
            log.error("解析分析结果失败: {}", e.getMessage(), e);
            return createDefaultAnalysisResult(response);
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        Pattern pattern = Pattern.compile("\\{[^{}]*(?:\\{[^{}]*\\}[^{}]*)*\\}");
        Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            return matcher.group();
        }
        throw new RuntimeException("无法从响应中提取JSON");
    }

    /**
     * 解析分类
     */
    private MessageCategory parseCategory(String categoryStr) {
        try {
            return MessageCategory.valueOf(categoryStr);
        } catch (Exception e) {
            return MessageCategory.OTHER;
        }
    }

    /**
     * 解析关键词
     */
    private List<String> parseKeywords(com.fasterxml.jackson.databind.JsonNode keywordsNode) {
        try {
            if (keywordsNode.isArray()) {
                return objectMapper.convertValue(keywordsNode, List.class);
            }
        } catch (Exception e) {
            log.warn("解析关键词失败: {}", e.getMessage());
        }
        return List.of();
    }

    /**
     * 解析时间
     */
    private LocalDateTime parseDateTime(com.fasterxml.jackson.databind.JsonNode dateTimeNode) {
        try {
            if (dateTimeNode != null && !dateTimeNode.isNull()) {
                String dateTimeStr = dateTimeNode.asText();
                return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        } catch (Exception e) {
            log.warn("解析时间失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 创建默认分析结果
     */
    private MessageAnalysisResult createDefaultAnalysisResult(String content) {
        return MessageAnalysisResult.builder()
                .summary(content.length() > 50 ? content.substring(0, 50) + "..." : content)
                .category(MessageCategory.OTHER)
                .keywords(List.of())
                .importanceScore(5)
                .needReminder(false)
                .confidence(0.5)
                .analysisNote("AI分析失败，使用默认结果")
                .build();
    }
}
