package com.wedin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * JWT配置类
 */
@Configuration
@ConfigurationProperties(prefix = "jwt")
@Data
public class JwtConfig {

    /**
     * JWT密钥
     */
    private String secret = "wedin-ai-assistant-jwt-secret-key-2024-very-long-and-secure";

    /**
     * Access Token过期时间（毫秒）
     * 默认24小时
     */
    private long accessTokenExpiration = 24 * 60 * 60 * 1000;

    /**
     * Refresh Token过期时间（毫秒）
     * 默认7天
     */
    private long refreshTokenExpiration = 7 * 24 * 60 * 60 * 1000;

    /**
     * Token发行者
     */
    private String issuer = "wedin-ai-assistant";

    /**
     * Token前缀
     */
    private String tokenPrefix = "Bearer ";

    /**
     * Token请求头名称
     */
    private String headerName = "Authorization";
}
