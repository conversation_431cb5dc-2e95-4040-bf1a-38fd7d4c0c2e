# 🎰部署指南
```
cd backend
docker build -f Dockerfile -t wedin-ai-assistant .
docker-compose  -f docker-compose.yml up -d

docker system prune -f
docker rmi wedin-ai-assistant 2>/dev/null || true


docker-compose down
docker-compose up -d --build
docker-compose logs -f wedin-backend
```


# 🔐 创建安全用户指南

## 🛠️ 创建安全用户的方法

### 方法1：使用在线BCrypt工具（推荐）

1. **生成BCrypt密码哈希**：
   - 访问：https://bcrypt-generator.com/
   - 或：https://www.browserling.com/tools/bcrypt
   - 输入您的强密码
   - 选择 rounds: 12 (推荐)
   - 复制生成的哈希值

2. **直接插入数据库**：
```sql
-- 创建管理员用户（替换默认admin）
INSERT INTO users (
    username, 
    password, 
    email, 
    nickname, 
    role, 
    enabled, 
    account_non_expired, 
    account_non_locked, 
    credentials_non_expired,
    created_at,
    updated_at
) VALUES (
    'your_admin_username',
    '$2a$12$your_bcrypt_hash_here',  -- 替换为实际的BCrypt哈希
    '<EMAIL>',
    '系统管理员',
    'ADMIN',
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    NOW(),
    NOW()
);

-- 创建普通用户
INSERT INTO users (
    username, 
    password, 
    email, 
    nickname, 
    role, 
    enabled, 
    account_non_expired, 
    account_non_locked, 
    credentials_non_expired,
    created_at,
    updated_at
) VALUES (
    'your_username',
    '$2a$12$your_bcrypt_hash_here',  -- 替换为实际的BCrypt哈希
    '<EMAIL>',
    '用户昵称',
    'USER',
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    NOW(),
    NOW()
);
```

## 🚨 安全注意事项

1. **立即删除默认用户**:
```sql
DELETE FROM users WHERE username IN ('admin', 'testuser');
```

2. **定期更换密码**: 建议每3-6个月更换一次

3. **启用账户锁定**: 可以通过设置 `account_non_locked = FALSE` 临时锁定账户

4. **监控登录**: 检查 `last_login_at` 字段，发现异常登录