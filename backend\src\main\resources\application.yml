server:
  port: 8080
  servlet:
    context-path: /wedin

spring:
  application:
    name: wedin-ai-assistant
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/wedin_ai?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    open-in-view: false

  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

logging:
  level:
    com.wedin: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 微信公众号配置
wechat:
  app-id: ${WECHAT_APP_ID:your_app_id}
  app-secret: ${WECHAT_APP_SECRET:your_app_secret}
  token: ${WECHAT_TOKEN:your_token}
  encoding-aes-key: ${WECHAT_ENCODING_AES_KEY:your_encoding_aes_key}

# DeepSeek API配置
deepseek:
  api-key: ${DEEPSEEK_API_KEY:your_deepseek_api_key}
  base-url: https://api.deepseek.com/v1
  model: deepseek-chat
  max-tokens: 1000
  temperature: 0.7

# 智能回复配置
smart-reply:
  enabled: true
  delay:
    base: 2000      # 基础延迟2秒
    important: 1000 # 重要消息1秒
    normal: 3000    # 普通消息3秒
    low: 5000       # 低重要性5秒
  strategy:
    min-importance-score: 5
    reply-to-search: false
    reply-to-stats: false
    max-replies-per-day: 50
    min-reply-interval: 5

# 待办事项提醒配置
todo-reminder:
  enabled: true
  advance-minutes: 3 # 提前多少分钟提醒
  scan-interval: 60000 # 扫描间隔（毫秒）
  max-retry-count: 3   # 最大重试次数
  cleanup-days: 30     # 清理多少天前的已完成提醒

# 极光推送配置
jpush:
  enabled: true
  app-key: ${JPUSH_APP_KEY:31fcf148299a5afd6980c623}
  master-secret: ${JPUSH_MASTER_SECRET:7f6876d0f06681bbe9fc3139}
  production: false    # 是否为生产环境
  time-to-live: 86400  # 推送消息TTL (秒)

# 百度语音识别API配置
baidu:
  speech:
    api-key: ${BAIDU_SPEECH_API_KEY:test_api_key}
    secret-key: ${BAIDU_SPEECH_SECRET_KEY:test_secret_key}
    # 支持的音频格式
    supported-formats: webm,wav,pcm,m4a,aac
    # 最大文件大小 (字节)
    max-file-size: 10485760  # 10MB
    # 最大录音时长 (秒)
    max-duration: 60

# JWT配置
jwt:
  secret: ${JWT_SECRET:wedin-ai-assistant-jwt-secret-key-2024-very-long-and-secure}
  access-token-expiration: ${JWT_ACCESS_TOKEN_EXPIRATION:86400000}  # 24小时（毫秒）
  refresh-token-expiration: ${JWT_REFRESH_TOKEN_EXPIRATION:604800000}  # 7天（毫秒）
  issuer: ${JWT_ISSUER:wedin-ai-assistant}
  token-prefix: "Bearer "
  header-name: "Authorization"

# Swagger/OpenAPI 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    tryItOutEnabled: true
  show-actuator: true

# Spring Boot Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
