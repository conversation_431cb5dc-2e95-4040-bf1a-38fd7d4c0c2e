# WedIn AI Assistant 环境变量配置文件
# 复制此文件为 .env 并填入实际的配置值

# ===========================================
# 数据库配置
# ===========================================
DB_USERNAME=root
DB_PASSWORD=your_mysql_password

# 数据库主机配置（可选）
# 默认使用 host.docker.internal 连接宿主机MySQL
# 如果需要连接其他主机，请取消注释并修改
# DB_HOST=localhost
# DB_HOST=**********
# DB_HOST=your_mysql_server_ip
# DB_PORT=3306

# ===========================================
# 微信公众号配置
# ===========================================
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_TOKEN=your_wechat_token
WECHAT_ENCODING_AES_KEY=your_wechat_encoding_aes_key

# ===========================================
# DeepSeek API配置
# ===========================================
DEEPSEEK_API_KEY=your_deepseek_api_key

# ===========================================
# 极光推送配置
# ===========================================
JPUSH_APP_KEY=your_jpush_app_key
JPUSH_MASTER_SECRET=your_jpush_master_secret

# ===========================================
# 百度语音识别配置
# ===========================================
BAIDU_SPEECH_API_KEY=your_baidu_speech_api_key
BAIDU_SPEECH_SECRET_KEY=your_baidu_speech_secret_key

# ===========================================
# JWT配置（可选，有默认值）
# ===========================================
JWT_SECRET=wedin-ai-assistant-jwt-secret-key-2024-very-long-and-secure
JWT_ACCESS_TOKEN_EXPIRATION=86400000
JWT_REFRESH_TOKEN_EXPIRATION=604800000
JWT_ISSUER=wedin-ai-assistant

# ===========================================
# Spring配置
# ===========================================
SPRING_PROFILES_ACTIVE=prod
