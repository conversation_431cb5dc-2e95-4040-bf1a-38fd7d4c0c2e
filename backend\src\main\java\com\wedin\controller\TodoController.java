package com.wedin.controller;

import com.wedin.entity.TodoReminder;
import com.wedin.entity.TodoReminder;
import com.wedin.repository.TodoReminderRepository;
import com.wedin.service.TodoReminderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.List;

/**
 * 待办事项管理控制器
 */
@RestController
@RequestMapping("/api/todo")
@Slf4j
@RequiredArgsConstructor
@PreAuthorize("isAuthenticated()")
public class TodoController {

    private final TodoReminderService todoReminderService;
    private final TodoReminderRepository todoReminderRepository;

    /**
     * 获取用户的待办事项列表
     */
    @GetMapping("/list/{userOpenId}")
    public ResponseEntity<String> getTodoList(@PathVariable String userOpenId) {
        try {
            String todoList = todoReminderService.getUserTodoList(userOpenId);
            return ResponseEntity.ok(todoList);
        } catch (Exception e) {
            log.error("获取待办事项列表失败: userOpenId={}, error={}", userOpenId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body("获取待办事项列表失败");
        }
    }

    /**
     * 获取用户当前的提醒
     */
    @GetMapping("/reminders/{userOpenId}")
    public ResponseEntity<String> getCurrentReminders(@PathVariable String userOpenId) {
        try {
            String reminders = todoReminderService.getCurrentReminders(userOpenId);
            return ResponseEntity.ok(reminders);
        } catch (Exception e) {
            log.error("获取当前提醒失败: userOpenId={}, error={}", userOpenId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body("获取当前提醒失败");
        }
    }

    /**
     * 标记待办事项为已完成
     */
    @PostMapping("/complete/{userOpenId}")
    public ResponseEntity<String> completeTodo(
            @PathVariable String userOpenId,
            @RequestParam String todoContent) {
        try {
            boolean completed = todoReminderService.markTodoCompleted(userOpenId, todoContent);
            if (completed) {
                return ResponseEntity.ok("✅ 待办事项已标记为完成！");
            } else {
                return ResponseEntity.ok("未找到匹配的待办事项。");
            }
        } catch (Exception e) {
            log.error("标记待办事项完成失败: userOpenId={}, todoContent={}, error={}", 
                    userOpenId, todoContent, e.getMessage(), e);
            return ResponseEntity.internalServerError().body("标记待办事项完成失败");
        }
    }

    /**
     * 手动触发提醒扫描（用于测试）
     */
    @PostMapping("/scan")
    public ResponseEntity<String> triggerScan() {
        try {
            todoReminderService.scanAndSendReminders();
            return ResponseEntity.ok("提醒扫描已触发");
        } catch (Exception e) {
            log.error("触发提醒扫描失败: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("触发提醒扫描失败");
        }
    }

    /**
     * 手动触发清理过期提醒（用于测试）
     */
    @PostMapping("/cleanup")
    public ResponseEntity<String> triggerCleanup() {
        try {
            todoReminderService.cleanupExpiredReminders();
            return ResponseEntity.ok("过期提醒清理已触发");
        } catch (Exception e) {
            log.error("触发过期提醒清理失败: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("触发过期提醒清理失败");
        }
    }

    /**
     * 获取定时任务状态（简化版调试）
     */
    @GetMapping("/debug-status")
    public ResponseEntity<Map<String, Object>> getDebugStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 当前时间
            LocalDateTime now = LocalDateTime.now();
            status.put("currentTime", now.toString());

            // 查询所有提醒
            List<TodoReminder> allReminders = todoReminderRepository.findAll();
            status.put("totalReminders", allReminders.size());

            // 查询待处理提醒
            List<TodoReminder> pendingReminders = todoReminderRepository.findPendingReminders(now);
            status.put("pendingReminders", pendingReminders.size());

            // 提醒详情
            status.put("pendingDetails", pendingReminders);

            log.info("调试状态: 总提醒={}, 待处理={}", allReminders.size(), pendingReminders.size());

            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("获取调试状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 手动测试发送提醒（用于测试模拟消息功能）
     */
    @PostMapping("/test-reminder/{reminderId}")
    public ResponseEntity<String> testSendReminder(@PathVariable Long reminderId) {
        try {
            // 查找提醒记录
            var reminderOpt = todoReminderService.findById(reminderId);
            if (reminderOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            // 发送提醒
            todoReminderService.sendReminderMessage(reminderOpt.get());
            return ResponseEntity.ok("测试提醒已发送");
        } catch (Exception e) {
            log.error("测试发送提醒失败: reminderId={}, error={}", reminderId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body("测试发送提醒失败");
        }
    }
}
