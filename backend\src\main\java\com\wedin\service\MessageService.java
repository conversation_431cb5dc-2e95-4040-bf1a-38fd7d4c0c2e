package com.wedin.service;
import com.wedin.dto.MessageAnalysisResult;
import com.wedin.entity.Message;
import com.wedin.entity.MessageCategory;
import com.wedin.entity.TodoReminder;
import com.wedin.repository.MessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 消息服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MessageService {

    private final MessageRepository messageRepository;
    private final DeepSeekService deepSeekService;
    private final TodoReminderService todoReminderService;
    private final IntentRecognitionService intentRecognitionService;
    private final ConversationService conversationService;

    @Qualifier("messageAnalysisExecutor")
    private final Executor messageAnalysisExecutor;

    /**
     * 保存并分析消息
     */
    @Transactional public Message saveAndAnalyzeMessage(String userIdentifier, String content, String msgType, String toUserName) {
        try {
            // 保存原始消息
            Message message = Message.builder()
                    .userIdentifier(userIdentifier)
                    .type(msgType)
                    .content(content)
                    .aiStatus(Message.AiProcessStatus.PENDING)
                    .build();

            message = messageRepository.save(message);

            // 创建final引用用于lambda表达式
            final Message savedMessage = message;

            // 异步分析消息并生成智能回复（不阻塞响应）
            CompletableFuture.runAsync(() -> {
                try {
                    analyzeMessageAsync(savedMessage, toUserName);
                } catch (Exception e) {
                    log.error("异步分析消息失败: messageId={}, error={}", savedMessage.getId(), e.getMessage(), e);
                }
            }, messageAnalysisExecutor);

            return message;

        } catch (Exception e) {
            log.error("保存消息失败: userIdentifier={}, content={}, error={}", userIdentifier, content, e.getMessage(), e);
            throw new RuntimeException("保存消息失败", e);
        }
    }

    /**
     * 异步分析消息并生成智能回复
     */
    public void analyzeMessageAsync(Message message, String toUserName) {
        try {
            // 更新状态为处理中
            message.setAiStatus(Message.AiProcessStatus.PROCESSING);
            messageRepository.save(message);

            // 调用AI分析
            MessageAnalysisResult result = deepSeekService.analyzeMessage(message.getContent());

            // 更新消息分析结果
            message.setAiSummary(result.getSummary());
            message.setCategory(result.getCategory());
            message.setKeywords(String.join(",", result.getKeywords()));
            message.setImportanceScore(result.getImportanceScore());
            message.setNeedReminder(result.getNeedReminder());
            message.setReminderTime(result.getSuggestedReminderTime());
            message.setAiStatus(Message.AiProcessStatus.COMPLETED);

            message = messageRepository.save(message);

            // 如果是待办事项且有提醒时间，创建提醒记录
            log.info("检查是否需要创建待办事项提醒: messageId={}, category={}, reminderTime={}",
                    message.getId(), message.getCategory(), message.getReminderTime());

            if (message.getCategory() == MessageCategory.TODO && message.getReminderTime() != null) {
                try {
                    log.info("开始创建待办事项提醒: messageId={}", message.getId());
                    TodoReminder reminder = todoReminderService.createTodoReminder(message);
                    if (reminder != null) {
                        log.info("待办事项提醒创建成功: messageId={}, reminderId={}",
                                message.getId(), reminder.getId());
                    } else {
                        log.warn("待办事项提醒创建返回null: messageId={}", message.getId());
                    }
                } catch (Exception e) {
                    log.error("创建待办事项提醒失败: messageId={}, error={}",
                            message.getId(), e.getMessage(), e);
                }
            } else {
                log.info("不需要创建待办事项提醒: messageId={}, category={}, reminderTime={}",
                        message.getId(), message.getCategory(), message.getReminderTime());
            }

            log.info("消息分析完成: messageId={}, category={}, importance={}",
                    message.getId(), result.getCategory(), result.getImportanceScore());

            // 智能回复已在主流程中处理，这里不再发送额外回复
            log.debug("AI分析完成，智能回复已在主流程中处理: messageId={}", message.getId());

        } catch (Exception e) {
            log.error("消息分析失败: messageId={}, error={}", message.getId(), e.getMessage(), e);
            
            // 更新状态为失败
            message.setAiStatus(Message.AiProcessStatus.FAILED);
            messageRepository.save(message);
        }
    }

    /**
     * 搜索消息
     */
    public String searchMessages(String userIdentifier, String query) {
        try {
            // 先尝试关键词搜索
            List<Message> messages = messageRepository.searchByKeyword(userIdentifier, query);

            if (messages.isEmpty()) {
                // 如果没有找到，尝试分词搜索
                String[] keywords = query.split("\\s+");
                if (keywords.length >= 2) {
                    messages = messageRepository.searchByTwoKeywords(userIdentifier, keywords[0], keywords[1]);
                }
            }

            if (messages.isEmpty()) {
                return "没有找到相关的消息记录。您可以尝试使用其他关键词搜索。";
            }

            // 限制搜索结果数量
            List<String> messageContents = messages.stream()
                    .limit(10)
                    .map(this::formatMessageForSearch)
                    .collect(Collectors.toList());

            // 使用AI生成搜索结果
            return deepSeekService.searchMessages(query, messageContents);
            
        } catch (Exception e) {
            log.error("搜索消息失败: query={}, error={}", query, e.getMessage(), e);
            return "搜索过程中出现了问题，请稍后再试。";
        }
    }

    /**
     * 格式化消息用于搜索显示
     */
    private String formatMessageForSearch(Message message) {
        String time = message.getCreatedAt().toString();
        String category = message.getCategory() != null ? message.getCategory().getDisplayName() : "未分类";
        String content = message.getContent();
        String summary = message.getAiSummary();
        
        StringBuilder formatted = new StringBuilder();
        formatted.append("时间: ").append(time).append("\n");
        formatted.append("分类: ").append(category).append("\n");
        formatted.append("内容: ").append(content);
        
        if (summary != null && !summary.isEmpty()) {
            formatted.append("\n摘要: ").append(summary);
        }
        
        return formatted.toString();
    }

    /**
     * 获取用户最近的消息
     */
    public List<Message> getRecentMessages(String userIdentifier, int limit) {
        return messageRepository.findByUserIdentifierOrderByCreatedAtDesc(userIdentifier)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 根据分类获取消息
     */
    public List<Message> getMessagesByCategory(String userIdentifier, MessageCategory category) {
        return messageRepository.findByUserIdentifierAndCategoryOrderByCreatedAtDesc(userIdentifier, category);
    }

    /**
     * 获取需要提醒的消息
     */
    public List<Message> getMessagesNeedingReminder(String userIdentifier) {
        return messageRepository.findByUserIdentifierAndNeedReminderTrueAndReminderTimeBeforeOrderByReminderTimeAsc(
                userIdentifier, LocalDateTime.now());
    }

    /**
     * 获取重要消息
     */
    public List<Message> getImportantMessages(String userIdentifier, int minScore) {
        return messageRepository.findByUserIdentifierAndImportanceScoreGreaterThanEqualOrderByImportanceScoreDescCreatedAtDesc(
                userIdentifier, minScore);
    }

    /**
     * 获取用户统计信息
     */
    public String getUserStatistics(String userIdentifier) {
        long totalCount = messageRepository.countByUserIdentifier(userIdentifier);
        List<Object[]> categoryStats = messageRepository.countByUserIdentifierGroupByCategory(userIdentifier);

        StringBuilder stats = new StringBuilder();
        stats.append("📊 您的消息统计：\n\n");
        stats.append("总消息数：").append(totalCount).append(" 条\n\n");
        stats.append("分类统计：\n");

        for (Object[] stat : categoryStats) {
            MessageCategory category = (MessageCategory) stat[0];
            Long count = (Long) stat[1];
            stats.append("• ").append(category.getDisplayName()).append("：").append(count).append(" 条\n");
        }

        return stats.toString();
    }

    /**
     * 判断是否为搜索请求
     */
    public boolean isSearchRequest(String content) {
        String lowerContent = content.toLowerCase().trim();
        
        // 搜索关键词
        String[] searchKeywords = {
            "搜索", "查找", "找", "search", "find",
            "什么时候", "哪里", "谁", "怎么", "为什么",
            "记录", "说过", "提到", "关于"
        };
        
        for (String keyword : searchKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }
        
        // 问号结尾的问句
        if (lowerContent.endsWith("?") || lowerContent.endsWith("？")) {
            return true;
        }
        
        return false;
    }

    /**
     * 判断是否为统计请求
     */
    public boolean isStatisticsRequest(String content) {
        String lowerContent = content.toLowerCase().trim();

        String[] statsKeywords = {
            "统计", "总结", "汇总", "数量", "多少",
            "stats", "summary", "count"
        };

        for (String keyword : statsKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为待办事项查询请求
     */
    public boolean isTodoListRequest(String content) {
        String lowerContent = content.toLowerCase().trim();

        String[] todoKeywords = {
            "待办", "todo", "任务", "事项",
            "待办事项", "我的任务", "任务列表"
        };

        for (String keyword : todoKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为提醒查询请求
     */
    public boolean isReminderRequest(String content) {
        String lowerContent = content.toLowerCase().trim();

        // 如果包含时间信息，很可能是创建待办事项，不是查询
        if (containsTimeExpression(lowerContent)) {
            return false;
        }

        // 精确的查询关键词，避免与创建待办事项冲突
        String[] reminderKeywords = {
            "有什么提醒", "当前提醒", "最新提醒", "查看提醒",
            "我的提醒", "提醒列表", "reminder"
        };

        for (String keyword : reminderKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }

        // 单独的"提醒"关键词（不包含其他内容）
        if (lowerContent.equals("提醒")) {
            return true;
        }

        return false;
    }

    /**
     * 检查内容是否包含时间表达式
     */
    private boolean containsTimeExpression(String content) {
        String[] timeKeywords = {
            "点", "分", "时", "今天", "明天", "后天", "下周", "上午", "下午", "晚上", "中午", "夜里",
            "周一", "周二", "周三", "周四", "周五", "周六", "周日",
            "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"
        };

        for (String keyword : timeKeywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为完成待办事项的请求
     */
    public boolean isCompleteTodoRequest(String content) {
        String lowerContent = content.toLowerCase().trim();

        return lowerContent.startsWith("完成") ||
               lowerContent.startsWith("已完成") ||
               lowerContent.contains("完成了") ||
               lowerContent.equals("done") ||
               lowerContent.equals("完成");
    }

    /**
     * 从完成请求中提取待办事项内容
     */
    public String extractTodoContentFromCompleteRequest(String content) {
        String lowerContent = content.toLowerCase().trim();

        if (lowerContent.startsWith("完成")) {
            return content.substring(2).trim();
        } else if (lowerContent.startsWith("已完成")) {
            return content.substring(3).trim();
        } else if (lowerContent.contains("完成了")) {
            int index = lowerContent.indexOf("完成了");
            return content.substring(0, index).trim();
        }

        return content;
    }

    /**
     * 获取TodoReminderService实例
     */
    public TodoReminderService getTodoReminderService() {
        return todoReminderService;
    }

    /**
     * 仅保存消息（不进行分析）
     */
    @Transactional
    public Message saveMessage(String userIdentifier, String content, String msgType) {
        try {
            // 保存原始消息
            Message message = Message.builder()
                    .userIdentifier(userIdentifier)
                    .type(msgType)
                    .content(content)
                    .aiStatus(Message.AiProcessStatus.PENDING)
                    .build();

            message = messageRepository.save(message);
            log.info("消息保存成功: messageId={}, userIdentifier={}", message.getId(), userIdentifier);

            return message;
        } catch (Exception e) {
            log.error("保存消息失败: userIdentifier={}, content={}, error={}", userIdentifier, content, e.getMessage(), e);
            throw new RuntimeException("保存消息失败", e);
        }
    }

    /**
     * 获取用户最近的消息内容
     */
    public List<String> getRecentMessageContents(String userIdentifier, int limit) {
        try {
            var page = messageRepository.findByUserIdentifierOrderByCreatedAtDesc(
                userIdentifier, PageRequest.of(0, limit)
            );

            return page.getContent().stream()
                    .map(Message::getContent)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户最近消息失败: userIdentifier={}, error={}", userIdentifier, e.getMessage(), e);
            return List.of(); // 返回空列表
        }
    }

    // ========== 新增的PWA API支持方法 ==========

    /**
     * 处理并保存消息（PWA版本）
     */
    @Transactional
    public Message processAndSaveMessage(Message message) {
        try {
            log.info("开始处理消息: content={}, type={}, userIdentifier={}, userId={}",
                    message.getContent(), message.getType(),
                    message.getUserIdentifier(), message.getUserId());

            // 设置默认值
            if (message.getCreatedAt() == null) {
                message.setCreatedAt(LocalDateTime.now());
            }
            if (message.getAiStatus() == null) {
                message.setAiStatus(Message.AiProcessStatus.PENDING);
            }
            if (message.getConfirmationStatus() == null) {
                message.setConfirmationStatus(Message.ConfirmationStatus.PENDING);
            }
            // 设置消息来源标识
            if (message.getIsFromUser() == null) {
                message.setIsFromUser(true); // 用户发送的消息
            }
            if (message.getIsFromAI() == null) {
                message.setIsFromAI(false); // 用户消息不是来自AI
            }

            // 保存消息
            log.info("准备保存消息到数据库...");
            Message savedMessage = messageRepository.save(message);
            log.info("消息保存成功: messageId={}", savedMessage.getId());

            // 同步分析消息并生成AI回复
            try {
                analyzeMessageAndGenerateReply(savedMessage);
                log.info("消息分析和AI回复生成完成: messageId={}", savedMessage.getId());
            } catch (Exception e) {
                log.error("消息分析失败: messageId={}, error={}", savedMessage.getId(), e.getMessage(), e);
                // 设置默认值，确保消息有基本的分类信息
                savedMessage.setCategory(MessageCategory.OTHER);
                savedMessage.setImportanceScore(5);
                savedMessage.setAiStatus(Message.AiProcessStatus.FAILED);
                savedMessage.setAiResponse("AI分析暂时不可用，但您的消息已保存。");
                savedMessage = messageRepository.save(savedMessage);
            }

            return savedMessage;
        } catch (Exception e) {
            log.error("处理并保存消息失败: error={}", e.getMessage(), e);
            throw new RuntimeException("处理消息失败", e);
        }
    }

    /**
     * 同步分析消息并生成AI回复
     */
    private void analyzeMessageAndGenerateReply(Message message) {
        try {
            // 更新状态为处理中
            message.setAiStatus(Message.AiProcessStatus.PROCESSING);
            messageRepository.save(message);

            // 首先进行意图识别
            IntentRecognitionService.MessageIntent intent = intentRecognitionService.recognizeIntent(message.getContent());
            log.info("消息意图识别结果: messageId={}, intent={}, content={}", 
                    message.getId(), intent, message.getContent());

            if (intent == IntentRecognitionService.MessageIntent.CASUAL_CHAT) {
                // 普通对话，生成聊天回复
                String chatResponse = conversationService.generateChatResponse(message.getContent());

                // 更新用户消息信息 - 普通对话不设置分类
                message.setCategory(null); // 普通对话不显示分类
                message.setImportanceScore(1); // 对话消息重要性较低
                message.setNeedReminder(false);
                message.setAiSummary("普通对话");
                message.setKeywords("对话");
                message.setAiStatus(Message.AiProcessStatus.COMPLETED);
                message = messageRepository.save(message);

                // 创建AI回复消息
                createAIReplyMessage(message, chatResponse);

                log.info("处理为对话消息: messageId={}, response={}", message.getId(), chatResponse);
            } else {
                // 需要分类记录的消息，进行详细分析
                MessageAnalysisResult result = deepSeekService.analyzeMessage(message.getContent());

                // 更新消息分析结果
                message.setAiSummary(result.getSummary());
                message.setCategory(result.getCategory());
                message.setKeywords(String.join(",", result.getKeywords()));
                message.setImportanceScore(result.getImportanceScore());
                message.setNeedReminder(result.getNeedReminder());
                message.setReminderTime(result.getSuggestedReminderTime());
                message.setAiStatus(Message.AiProcessStatus.COMPLETED);
                message = messageRepository.save(message);

                // 生成智能回复并创建AI回复消息
                try {
                    String smartReply = generateSmartReplyForMessage(message);
                    if (smartReply != null && !smartReply.trim().isEmpty()) {
                        createAIReplyMessage(message, smartReply);
                        log.info("智能回复生成成功: messageId={}, reply={}", message.getId(), smartReply);
                    }
                } catch (Exception replyError) {
                    log.error("生成智能回复失败: messageId={}, error={}", message.getId(), replyError.getMessage());
                }
                
                log.info("处理为分类记录消息: messageId={}, category={}", message.getId(), result.getCategory());
            }

            // 只对分类记录的消息创建待办提醒（对话消息不创建提醒）
            if (intent == IntentRecognitionService.MessageIntent.RECORD_NEEDED &&
                message.getCategory() == MessageCategory.TODO && 
                message.getReminderTime() != null) {
                try {
                    TodoReminder reminder = todoReminderService.createTodoReminder(message);
                    log.info("PWA待办事项提醒创建成功: messageId={}, reminderId={}",
                            message.getId(), reminder != null ? reminder.getId() : "null");
                } catch (Exception e) {
                    log.error("PWA创建待办事项提醒失败: messageId={}, error={}",
                            message.getId(), e.getMessage(), e);
                }
            }

            log.info("PWA消息分析完成: messageId={}, category={}, importance={}",
                    message.getId(), message.getCategory(), message.getImportanceScore());

        } catch (Exception e) {
            log.error("消息分析失败: messageId={}, error={}", message.getId(), e.getMessage(), e);

            // 更新状态为失败
            message.setAiStatus(Message.AiProcessStatus.FAILED);
            message.setAiResponse("AI分析失败，但您的消息已保存。");
            messageRepository.save(message);
        }
    }

    /**
     * 创建AI回复消息
     */
    private void createAIReplyMessage(Message originalMessage, String replyContent) {
        try {
            Message aiReply = Message.builder()
                    .userId(originalMessage.getUserId())
                    .userIdentifier(originalMessage.getUserIdentifier())
                    .type("AI_REPLY")
                    .content(replyContent)
                    .aiResponse(replyContent)
                    .aiStatus(Message.AiProcessStatus.COMPLETED)
                    .confirmationStatus(Message.ConfirmationStatus.CONFIRMED)
                    .isFromAI(true)
                    .isFromUser(false)
                    .parentMessageId(originalMessage.getId())
                    .category(originalMessage.getCategory()) // 继承原消息的分类（普通对话为null）
                    .createdAt(LocalDateTime.now())
                    .build();

            Message savedReply = messageRepository.save(aiReply);
            log.info("创建AI回复消息成功: originalMessageId={}, replyId={}, reply={}",
                    originalMessage.getId(), savedReply.getId(), replyContent);

        } catch (Exception e) {
            log.error("创建AI回复消息失败: originalMessageId={}, error={}",
                    originalMessage.getId(), e.getMessage(), e);
        }
    }

    /**
     * 为消息生成智能回复
     */
    private String generateSmartReplyForMessage(Message message) {
        try {
            // 获取用户最近的消息历史
            String userIdentifier = message.getUserIdentifier() != null ?
                message.getUserIdentifier() : "user_" + message.getUserId();
            List<String> recentMessages = getRecentMessageContents(userIdentifier, 5);

            // 生成智能回复
            return deepSeekService.generateSmartReply(
                message.getContent(),
                message.getCategory(),
                recentMessages,
                message.getImportanceScore()
            );
        } catch (Exception e) {
            log.error("生成智能回复失败: messageId={}, error={}", message.getId(), e.getMessage(), e);
            return generateDefaultReplyForCategory(message.getCategory(), message.getImportanceScore());
        }
    }

    /**
     * 根据分类生成默认回复
     */
    private String generateDefaultReplyForCategory(MessageCategory category, Integer importanceScore) {
        if (category == null) {
            return "收到您的消息，我已经为您记录下来了。";
        }

        return switch (category) {
            case TODO -> "好的，我已经记录了这个待办事项" +
                        (importanceScore != null && importanceScore >= 7 ? "，这看起来很重要，请注意及时处理。" : "。");
            case IMPORTANT_INFO -> "重要信息已记录" +
                                 (importanceScore != null && importanceScore >= 8 ? "，我会特别关注这条信息。" : "。");
            case CONTACT_INFO -> "联系信息已保存，需要时可以随时查找。";
            case LEARNING_NOTE -> "学习笔记已记录，持续学习很棒！";
            case PERSONAL_THOUGHT -> "您的想法已记录下来，随时可以回顾。";
            case FINANCIAL -> "财务信息已安全记录。";
            case HEALTH -> "健康相关信息已记录，注意身体健康。";
            case ENTERTAINMENT -> "娱乐信息已记录，劳逸结合很重要。";
            default -> "消息已收到并分类记录。";
        };
    }

    /**
     * 分页查询所有消息
     */
    public Page<Message> findAll(Pageable pageable) {
        return messageRepository.findAll(pageable);
    }
    
    /**
     * 分页查询指定时间之后的消息
     */
    public Page<Message> findMessagesAfterTime(LocalDateTime afterTime, Pageable pageable) {
        return messageRepository.findByCreatedAtAfterOrderByCreatedAtDesc(afterTime, pageable);
    }

    /**
     * 分页查询用户的所有消息
     */
    public Page<Message> findUserMessages(String userIdentifier, Pageable pageable) {
        return messageRepository.findByUserIdentifierOrderByCreatedAtDesc(userIdentifier, pageable);
    }

    /**
     * 分页查询用户指定时间之后的消息
     */
    public Page<Message> findUserMessagesAfterTime(String userIdentifier, LocalDateTime afterTime, Pageable pageable) {
        log.info("查询用户指定时间之后的消息: userIdentifier={}, afterTime={}, page={}, size={}",
                userIdentifier, afterTime, pageable.getPageNumber(), pageable.getPageSize());

        Page<Message> result = messageRepository.findByUserIdentifierAndCreatedAtAfterOrderByCreatedAtDesc(userIdentifier, afterTime, pageable);

        log.info("查询结果: 找到{}条消息", result.getTotalElements());

        return result;
    }

    /**
     * 根据分类分页查询消息
     */
    public Page<Message> findByCategory(MessageCategory category, Pageable pageable) {
        return messageRepository.findByCategoryOrderByCreatedAtDesc(category, pageable);
    }

    /**
     * 根据用户标识和分类分页查询消息
     */
    public Page<Message> findByUserIdentifierAndCategory(String userIdentifier, MessageCategory category, Pageable pageable) {
        return messageRepository.findByUserIdentifierAndCategoryOrderByCreatedAtDesc(userIdentifier, category, pageable);
    }

    /**
     * 搜索消息（PWA版本）
     */
    public Page<Message> searchMessages(String query, MessageCategory category,
                                       String startDate, String endDate, Pageable pageable) {
        try {
            LocalDateTime start = null;
            LocalDateTime end = null;

            if (startDate != null && !startDate.isEmpty()) {
                start = LocalDateTime.parse(startDate + " 00:00:00",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            if (endDate != null && !endDate.isEmpty()) {
                end = LocalDateTime.parse(endDate + " 23:59:59",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            if (category != null && start != null && end != null) {
                return messageRepository.searchByCategoryAndDateRange(query, category, start, end, pageable);
            } else if (category != null) {
                return messageRepository.searchByCategory(query, category, pageable);
            } else if (start != null && end != null) {
                return messageRepository.searchByDateRange(query, start, end, pageable);
            } else {
                return messageRepository.searchByContent(query, pageable);
            }
        } catch (Exception e) {
            log.error("搜索消息失败: query={}, error={}", query, e.getMessage(), e);
            return Page.empty(pageable);
        }
    }

    /**
     * 根据ID查找消息
     */
    public Optional<Message> findById(Long id) {
        return messageRepository.findById(id);
    }

    /**
     * 保存消息
     */
    public Message save(Message message) {
        return messageRepository.save(message);
    }

    /**
     * 检查消息是否存在
     */
    public boolean existsById(Long id) {
        return messageRepository.existsById(id);
    }

    /**
     * 删除消息
     */
    public void deleteById(Long id) {
        messageRepository.deleteById(id);
    }

    /**
     * 获取分类统计
     */
    public Map<String, Long> getCategoryStats() {
        try {
            List<Object[]> results = messageRepository.countByCategory();
            Map<String, Long> stats = new HashMap<>();

            for (Object[] result : results) {
                MessageCategory category = (MessageCategory) result[0];
                Long count = (Long) result[1];
                stats.put(category.name(), count);
            }

            return stats;
        } catch (Exception e) {
            log.error("获取分类统计失败: error={}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 获取有提醒的消息列表
     */
    public List<Message> findMessagesWithReminders() {
        try {
            return messageRepository.findByNeedReminderTrueOrderByReminderTimeAsc();
        } catch (Exception e) {
            log.error("获取提醒消息失败: error={}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 确认AI理解结果
     */
    @Transactional
    public Message confirmMessage(Long messageId) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("消息不存在");
        }

        Message message = messageOpt.get();
        message.setConfirmationStatus(Message.ConfirmationStatus.CONFIRMED);
        message.setUpdatedAt(LocalDateTime.now());

        Message savedMessage = messageRepository.save(message);

        // 创建AI反馈消息
        createAutoReplyMessage(savedMessage, "confirm", "好的，我已经记录下来了！如果您还有其他需要分类的内容，随时告诉我。");

        return savedMessage;
    }

    /**
     * 拒绝AI理解结果
     */
    @Transactional
    public Message rejectMessage(Long messageId, String reason) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("消息不存在");
        }

        Message message = messageOpt.get();
        message.setConfirmationStatus(Message.ConfirmationStatus.REJECTED);
        message.setUpdatedAt(LocalDateTime.now());

        // 可以将拒绝原因记录到AI回复中
        if (reason != null && !reason.trim().isEmpty()) {
            message.setAiResponse("用户拒绝原因: " + reason + "\n\n" + (message.getAiResponse() != null ? message.getAiResponse() : ""));
        }

        Message savedMessage = messageRepository.save(message);

        // 创建AI反馈消息
        createAutoReplyMessage(savedMessage, "reject", "没关系，请您重新描述一下，我会重新理解和分类。");

        return savedMessage;
    }

    /**
     * 修改AI理解结果
     */
    @Transactional
    public Message modifyMessage(Long messageId, Object modifyRequest) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("消息不存在");
        }

        Message message = messageOpt.get();
        message.setConfirmationStatus(Message.ConfirmationStatus.MODIFIED);
        message.setUpdatedAt(LocalDateTime.now());

        // 这里需要根据modifyRequest更新消息字段
        // 由于类型限制，暂时简化处理

        Message savedMessage = messageRepository.save(message);

        // 创建AI反馈消息
        createAutoReplyMessage(savedMessage, "modify", "好的，我已经按照您的要求更新了分类信息！");

        return savedMessage;
    }

    /**
     * 删除消息
     */
    @Transactional
    public void deleteMessage(Long id) {
        messageRepository.deleteById(id);
    }

    /**
     * 创建自动回复消息
     */
    @Transactional
    public Message createAutoReplyMessage(Message originalMessage, String actionType, String replyContent) {
        try {
            // 创建AI反馈消息
            Message autoReply = Message.builder()
                    .userId(originalMessage.getUserId())
                    .userIdentifier(originalMessage.getUserIdentifier())
                    .type("AUTO_REPLY")
                    .content(replyContent)
                    .aiResponse(replyContent)
                    .aiStatus(Message.AiProcessStatus.COMPLETED)
                    .confirmationStatus(Message.ConfirmationStatus.CONFIRMED)
                    .isFromAI(true)
                    .isFromUser(false)
                    .parentMessageId(originalMessage.getId())
                    .category(MessageCategory.OTHER)
                    .createdAt(LocalDateTime.now())
                    .build();

            Message savedReply = messageRepository.save(autoReply);
            log.info("创建自动回复消息成功: originalMessageId={}, replyId={}, actionType={}", 
                    originalMessage.getId(), savedReply.getId(), actionType);

            // 移除SSE推送，自动回复消息直接保存到数据库

            return savedReply;
        } catch (Exception e) {
            log.error("创建自动回复消息失败: originalMessageId={}, actionType={}, error={}", 
                    originalMessage.getId(), actionType, e.getMessage(), e);
            return null;
        }
    }
}
