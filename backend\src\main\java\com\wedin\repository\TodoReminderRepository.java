package com.wedin.repository;

import com.wedin.entity.TodoReminder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 待办事项提醒数据访问层
 */
@Repository
public interface TodoReminderRepository extends JpaRepository<TodoReminder, Long> {

    /**
     * 根据消息ID查询提醒
     */
    TodoReminder findByMessageId(Long messageId);

    /**
     * 根据用户OpenID查询所有提醒
     */
    List<TodoReminder> findByUserOpenIdOrderByTriggerTimeAsc(String userOpenId);

    /**
     * 根据用户OpenID和状态查询提醒
     */
    List<TodoReminder> findByUserOpenIdAndStatusOrderByTriggerTimeAsc(
            String userOpenId, TodoReminder.ReminderStatus status);

    /**
     * 查询需要发送提醒的记录
     * 条件：状态为PENDING，提醒时间已到或已过
     */
    @Query("SELECT tr FROM TodoReminder tr WHERE tr.status = 'PENDING' " +
           "AND tr.reminderTime <= :currentTime ORDER BY tr.reminderTime ASC")
    List<TodoReminder> findPendingReminders(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询即将过期的提醒（触发时间已过但状态仍为PENDING）
     */
    @Query("SELECT tr FROM TodoReminder tr WHERE tr.status = 'PENDING' " +
           "AND tr.triggerTime < :currentTime ORDER BY tr.triggerTime ASC")
    List<TodoReminder> findExpiredReminders(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据状态统计提醒数量
     */
    long countByStatus(TodoReminder.ReminderStatus status);

    /**
     * 根据状态查询提醒列表
     */
    List<TodoReminder> findByStatus(TodoReminder.ReminderStatus status);

    /**
     * 根据消息ID查找提醒列表（用于测试）
     */
    List<TodoReminder> findAllByMessageId(Long messageId);

    /**
     * 根据用户OpenID查询今天的待办事项
     */
    @Query("SELECT tr FROM TodoReminder tr WHERE tr.userOpenId = :userOpenId " +
           "AND DATE(tr.triggerTime) = DATE(:today) " +
           "AND tr.status IN ('PENDING', 'REMINDED') " +
           "ORDER BY tr.triggerTime ASC")
    List<TodoReminder> findTodayTodos(@Param("userOpenId") String userOpenId, 
                                     @Param("today") LocalDateTime today);

    /**
     * 根据用户OpenID查询明天的待办事项
     */
    @Query("SELECT tr FROM TodoReminder tr WHERE tr.userOpenId = :userOpenId " +
           "AND DATE(tr.triggerTime) = DATE(:tomorrow) " +
           "AND tr.status IN ('PENDING', 'REMINDED') " +
           "ORDER BY tr.triggerTime ASC")
    List<TodoReminder> findTomorrowTodos(@Param("userOpenId") String userOpenId, 
                                        @Param("tomorrow") LocalDateTime tomorrow);

    /**
     * 统计用户的待办事项数量
     */
    @Query("SELECT COUNT(tr) FROM TodoReminder tr WHERE tr.userOpenId = :userOpenId " +
           "AND tr.status = :status")
    long countByUserOpenIdAndStatus(@Param("userOpenId") String userOpenId, 
                                   @Param("status") TodoReminder.ReminderStatus status);

    /**
     * 查询用户最近的待办事项
     */
    @Query("SELECT tr FROM TodoReminder tr WHERE tr.userOpenId = :userOpenId " +
           "ORDER BY tr.createdAt DESC")
    List<TodoReminder> findRecentTodos(@Param("userOpenId") String userOpenId);
}
