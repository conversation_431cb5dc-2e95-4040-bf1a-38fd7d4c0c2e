package com.wedin.entity;

/**
 * 消息分类枚举
 * AI自动分类的消息类型
 */
public enum MessageCategory {
    
    /**
     * 待办事项 - 需要用户执行的任务
     */
    TODO("待办事项", "需要用户执行的任务或提醒事项"),
    
    /**
     * 重要信息 - 需要记住的重要内容
     */
    IMPORTANT_INFO("重要信息", "重要的信息、数据或联系方式"),
    
    /**
     * 联系人 - 联系方式或地址信息
     */
    CONTACT_INFO("联系人", "联系方式、地址或位置信息"),

    /**
     * 学习笔记 - 学习相关的内容
     */
    LEARNING_NOTE("学习笔记", "学习资料、笔记或知识点"),
    
    /**
     * 个人想法 - 个人思考或灵感
     */
    PERSONAL_THOUGHT("个人想法", "个人思考、灵感或创意"),
    
    /**
     * 财务相关 - 财务、账单或消费记录
     */
    FINANCIAL("财务相关", "财务信息、账单或消费记录"),
    
    /**
     * 健康相关 - 健康、医疗或运动相关
     */
    HEALTH("健康相关", "健康状况、医疗信息或运动记录"),
    
    /**
     * 娱乐休闲 - 娱乐、休闲或兴趣相关
     */
    ENTERTAINMENT("娱乐休闲", "娱乐、休闲活动或兴趣爱好"),
    
    /**
     * 其他 - 无法明确分类的内容
     */
    OTHER("其他", "无法明确分类的内容");

    private final String displayName;
    private final String description;

    MessageCategory(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据显示名称获取枚举值
     */
    public static MessageCategory fromDisplayName(String displayName) {
        for (MessageCategory category : values()) {
            if (category.displayName.equals(displayName)) {
                return category;
            }
        }
        return OTHER;
    }
}
