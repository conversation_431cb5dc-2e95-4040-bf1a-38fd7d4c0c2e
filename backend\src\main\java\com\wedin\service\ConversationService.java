package com.wedin.service;

import com.wedin.dto.DeepSeekRequest;
import com.wedin.dto.DeepSeekResponse;
import com.wedin.config.DeepSeekConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 对话服务
 * 处理普通聊天对话，不进行信息分类和记录
 */
@Service
@Slf4j
public class ConversationService {

    private final DeepSeekConfig deepSeekConfig;
    private final WebClient webClient;

    public ConversationService(DeepSeekConfig deepSeekConfig) {
        this.deepSeekConfig = deepSeekConfig;
        this.webClient = WebClient.builder()
                .baseUrl(deepSeekConfig.getBaseUrl())
                .defaultHeader("Authorization", "Bearer " + deepSeekConfig.getApiKey())
                .defaultHeader("Content-Type", "application/json")
                .build();
    }

    /**
     * 生成对话回复
     * @param userMessage 用户消息
     * @return AI回复
     */
    public String generateChatResponse(String userMessage) {
        try {
            log.info("开始生成对话回复: userMessage={}", userMessage);
            String prompt = buildChatPrompt(userMessage);
            String response = callDeepSeekAPI(prompt);
            log.info("DeepSeek API对话回复成功: response={}", response);
            return response;
        } catch (Exception e) {
            log.error("生成对话回复失败，使用默认回复: {}", e.getMessage(), e);
            String defaultResponse = generateDefaultChatResponse(userMessage);
            log.info("使用默认对话回复: response={}", defaultResponse);
            return defaultResponse;
        }
    }

    /**
     * 构建对话提示词
     */
    private String buildChatPrompt(String userMessage) {
        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
        String dayOfWeek = getDayOfWeekInChinese(now.getDayOfWeek());

        return String.format("""
            你是一个智能助理，名叫"小助"。现在用户想要和你聊天。

            当前时间：%s (%s %s)

            用户消息："%s"

            重要规则：
            1. 这是普通对话，不是要记录信息
            2. 回复要简短、自然、口语化
            3. 对于天气询问，给出简单的回应即可，不要详细预报
            4. 对于时间询问，直接告诉当前时间
            5. 保持轻松友好的语调
            6. 回复控制在1-2句话内
            7. 不要主动提及记录功能
            8. 绝对不要说"我理解您的意思是"、"已保存到"等词语
            9. 不要分析用户意图，直接回应即可

            请直接回复用户，语气要像朋友聊天一样轻松自然。
            """, currentTime, currentDate, dayOfWeek, userMessage);
    }

    /**
     * 获取中文星期
     */
    private String getDayOfWeekInChinese(java.time.DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> "星期一";
            case TUESDAY -> "星期二";
            case WEDNESDAY -> "星期三";
            case THURSDAY -> "星期四";
            case FRIDAY -> "星期五";
            case SATURDAY -> "星期六";
            case SUNDAY -> "星期日";
        };
    }

    /**
     * 调用DeepSeek API
     */
    private String callDeepSeekAPI(String prompt) {
        DeepSeekRequest request = DeepSeekRequest.builder()
                .model(deepSeekConfig.getModel())
                .maxTokens(300) // 对话回复适中长度
                .temperature(0.7) // 较高温度让对话更自然
                .stream(false)
                .messages(List.of(
                        DeepSeekRequest.ChatMessage.builder()
                                .role("user")
                                .content(prompt)
                                .build()
                ))
                .build();

        Mono<DeepSeekResponse> responseMono = webClient.post()
                .uri("/chat/completions")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(DeepSeekResponse.class);

        DeepSeekResponse response = responseMono.block();
        
        if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
            return response.getChoices().get(0).getMessage().getContent();
        }
        
        throw new RuntimeException("DeepSeek API调用失败");
    }

    /**
     * 生成默认对话回复
     */
    private String generateDefaultChatResponse(String userMessage) {
        String content = userMessage.toLowerCase().trim();

        // 问候回复
        if (content.contains("你好") || content.contains("hi") || content.contains("hello")) {
            return "你好！有什么可以聊的吗？";
        }

        // 询问身份
        if (content.contains("你是谁") || content.contains("你叫什么")) {
            return "我是小助，你的智能助理。";
        }

        // 询问功能
        if (content.contains("你能做什么") || content.contains("你会什么") || content.contains("你的功能")) {
            return "我可以帮你记录待办事项、重要信息，也可以聊天。";
        }

        // 感谢
        if (content.contains("谢谢") || content.contains("感谢")) {
            return "不客气！";
        }

        // 时间询问
        if (content.contains("几点") || content.contains("时间")) {
            LocalDateTime now = LocalDateTime.now();
            return String.format("现在是%s。", now.format(DateTimeFormatter.ofPattern("HH:mm")));
        }

        // 天气询问
        if (content.contains("天气") || content.contains("下雨")) {
            return "我无法查看实时天气，建议你查看天气应用哦。";
        }

        // 默认回复
        return "嗯，我明白了。还有什么想聊的吗？";
    }
} 