package com.wedin.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
public class BaiduSpeechService {

    @Value("${baidu.speech.api-key}")
    private String apiKey;

    @Value("${baidu.speech.secret-key}")
    private String secretKey;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // 缓存访问令牌
    private String cachedAccessToken;
    private long tokenExpireTime;

    public BaiduSpeechService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * 获取访问令牌
     */
    public String getAccessToken() throws Exception {
        // 检查缓存的令牌是否有效
        if (cachedAccessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return cachedAccessToken;
        }

        String url = "https://aip.baidubce.com/oauth/2.0/token";
        
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", "client_credentials");
        params.add("client_id", apiKey);
        params.add("client_secret", secretKey);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> result = objectMapper.readValue(response.getBody(), Map.class);
            
            if (result.containsKey("access_token")) {
                cachedAccessToken = (String) result.get("access_token");
                // 设置过期时间为29天（百度令牌有效期30天）
                tokenExpireTime = System.currentTimeMillis() + 29 * 24 * 60 * 60 * 1000L;
                return cachedAccessToken;
            } else {
                throw new Exception("获取访问令牌失败: " + result.get("error_description"));
            }
        } else {
            throw new Exception("HTTP请求失败: " + response.getStatusCode());
        }
    }

    /**
     * 语音识别
     */
    public Map<String, Object> recognizeAudio(byte[] audioData, String format, int rate, int channel, String cuid) throws Exception {
        // 检查是否为测试模式
        if ("test_api_key".equals(apiKey) || "test_secret_key".equals(secretKey)) {
            // 返回模拟结果用于测试
            Map<String, Object> mockResult = new HashMap<>();
            mockResult.put("err_no", 0);
            mockResult.put("err_msg", "success");
            mockResult.put("corpus_no", "12345");
            mockResult.put("sn", "123456789");
            mockResult.put("result", new String[]{"这是测试语音识别结果"});
            return mockResult;
        }
        
        // 处理格式转换
        String actualFormat = format;
        if ("webm".equals(format) || "pcm".equals(format)) {
            // webm和pcm都使用pcm格式发送给百度API
            actualFormat = "pcm";
        } else if ("m4a".equals(format)) {
            // m4a格式百度API直接支持
            actualFormat = "m4a";
        } else if ("amr".equals(format)) {
            // AMR格式百度API直接支持
            actualFormat = "amr";
        } else if ("wav".equals(format)) {
            // WAV格式百度API直接支持
            actualFormat = "wav";
        } else {
            // 未知格式，记录警告但继续处理
            System.out.println("警告: 未知音频格式 " + format + "，将直接传递给百度API");
            actualFormat = format;
        }

        System.out.println("音频格式转换: " + format + " -> " + actualFormat);
        
        String accessToken = getAccessToken();
        
        String url = "https://vop.baidu.com/server_api";
        
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("format", actualFormat);
        params.put("rate", rate);
        params.put("channel", channel);
        params.put("cuid", cuid);
        params.put("token", accessToken);
        params.put("speech", Base64.getEncoder().encodeToString(audioData));
        params.put("len", audioData.length);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("百度API响应状态: " + response.getStatusCode());
        System.out.println("百度API响应内容: " + response.getBody());

        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> result = objectMapper.readValue(response.getBody(), Map.class);

            // 检查API返回的错误码
            Object errNo = result.get("err_no");
            if (errNo != null && !errNo.equals(0)) {
                System.out.println("百度API返回错误: err_no=" + errNo + ", err_msg=" + result.get("err_msg"));
            }

            return result;
        } else {
            throw new Exception("语音识别API调用失败: " + response.getStatusCode());
        }
    }
}