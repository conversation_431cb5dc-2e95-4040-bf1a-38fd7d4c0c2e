package com.wedin.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * WebClient配置
 */
@Configuration
@RequiredArgsConstructor
public class WebClientConfig {

    private final DeepSeekConfig deepSeekConfig;

    @Bean
    public WebClient webClient() {
        return WebClient.builder()
                .baseUrl(deepSeekConfig.getBaseUrl())
                .defaultHeader("Authorization", "Bearer " + deepSeekConfig.getApiKey())
                .defaultHeader("Content-Type", "application/json")
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024)) // 1MB
                .build();
    }
}
